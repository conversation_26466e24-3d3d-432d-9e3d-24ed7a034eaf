<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.1.3" targetFramework="net45" />
  <package id="EntityFramework.zh-Hans" version="6.1.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.Mvc" version="4.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.Mvc.FixedDisplayModes" version="1.0.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.Mvc.zh-Hans" version="4.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.Razor" version="2.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.Razor.zh-Hans" version="2.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi" version="4.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Client" version="4.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Client.zh-Hans" version="4.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Core" version="4.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Core.zh-Hans" version="4.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="4.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.WebHost.zh-Hans" version="4.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebPages" version="2.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebPages.zh-Hans" version="2.0.30506.0" targetFramework="net45" />
  <package id="Microsoft.Net.Http" version="2.0.20710.0" targetFramework="net45" />
  <package id="Microsoft.Net.Http.zh-Hans" version="2.0.20710.0" targetFramework="net45" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net45" />
  <package id="Newtonsoft.Json" version="4.5.11" targetFramework="net45" />
</packages>