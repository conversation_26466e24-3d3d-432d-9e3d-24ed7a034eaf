@{
    ViewBag.Title = "添加附件";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@using (Ajax.BeginForm("AddCharacter", "MaterialContent", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">回复内容：</label>
        <div class="col-sm-9">
            <textarea id="tag_matter" name="tag_matter" style="width:650px;height:300px;"></textarea>
        </div>
    </div>

    <input id="content_id" name="content_id" hidden="hidden" value="@ViewBag.content_id"/>

}
<script>
    function Save() {
        //开始提交
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("添加成功！");
            window.location = "Enclosure_Index?content_id="+@ViewBag.content_id;
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>
