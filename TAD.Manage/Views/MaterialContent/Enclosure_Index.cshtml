@{
    ViewBag.Title = "内容附件";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('Index')" id="submitBTN"><span class="glyphicon  glyphicon-chevron-left"></span> 返回</button>
                    </td>
                    <td style="width:20px;"></td>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('AddCharacter_frm?content_id=' + getUrlParam('content_id'))" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加回复</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('AddImage_frm?content_id=' + getUrlParam('content_id'))" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加图片</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('AddVideo_frm?content_id=' + getUrlParam('content_id'))" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加视频</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>


        </form>
    </div>

    <table style=" min-height:150px;" id="table"
           data-toggle="table"
           data-url="GetEnclosurePage"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="tag_matter" data-sortable="true">目标内容</th>
                <th data-field="type_str" data-sortable="true">类型</th>
                <th data-field="thnumbnail" data-sortable="true" data-formatter="customer_head_img">封面</th>
                <th data-field="sort_code" data-sortable="true">排序</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
            </tr>
        </thead>
    </table>



</div>
<script type="text/javascript">
    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function customer_head_img(value, row, index) {
        return "<img style='width:60px; ' src='" + value + "?x-oss-process=image/resize,h_60' />"
    }

    function queryParams(params) {

        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),
            content_id: getUrlParam('content_id'),
        }
    }
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }

    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'
        html = html + '<li><a href="#" onclick="Edit(' + row.attachment_id + ' ,' + row.type_id + ' )">编辑</a></li>'
        html = html + '      <li><a href="#" onclick="Delete(' + row.attachment_id + ')">删除</a></li>'
        html = html + '      <li><a href="#" onclick="AddQrCode(' + row.attachment_id + ')">二维码</a></li>'
        html = html + '      </ul>     </div>'
        return html;
    }
    //设置附件
    function Enclosure(content_id) {
        open_url('Enclosure_frm?content_id=' + content_id);
    }
    //编辑
    function Edit(attachment_id, type_id) {
        switch (type_id) {
            case 1:
               open_url("EditImage_frm?attachment_id=" + attachment_id);
                break;
            case 2:
              open_url("EditVideo_frm?attachment_id=" + attachment_id);
                break;
            case 3:
               open_url("EditCharacter_frm?attachment_id=" + attachment_id);
                break;
        }
    }
    //删除
    function Delete(attachment_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("DeleteAttachment", "get", { attachment_id: attachment_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location.reload();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }
    //附加二维码
    function AddQrCode(attachment_id) {
        open_url("AddQrCode_frm?attachment_id=" + attachment_id);
    }
</script>