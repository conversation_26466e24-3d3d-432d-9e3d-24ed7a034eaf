@{
    ViewBag.Title = "编辑内容";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";

}
@model TAD.Model.material_content
@using (Ajax.BeginForm("Edit", "MaterialContent", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type_id" id="type_id">
                @foreach (TAD.Model.material_type item in (List<TAD.Model.material_type>)(ViewData["material_type"]))
                {
                    if (item.type_id == Model.type_id)
                    {
                        <option selected="selected" value="@item.type_id">@item.name</option>
                    }
                    else
                    {
                        <option value="@item.type_id">@item.name</option>
                    }

                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">分享次数：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="分享次数 是必需的。" name="share_count" value="@Model.share_count" placeholder="分享次数">
            <span class="field-validation-valid" data-valmsg-for="share_count" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="排序 是必需的。" name="sort_code" value="@Model.sort_code" placeholder="排序">
            <span class="field-validation-valid" data-valmsg-for="sort_code" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state == 1)
                {
                    <option value="1" selected="selected">启用</option>
                    <option value="0">禁用</option>
                }
                else
                {
                    <option value="1">启用</option>
                    <option value="0" selected="selected">禁用</option>
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">内容：</label>
        <div class="col-sm-9">
            <textarea id="content" name="content" style="width:650px;height:300px;">@Model.content</textarea>
        </div>
    </div>

    <input id="content_id" name="content_id" hidden="hidden" value="@Model.content_id" />
}

 
<script>
    function Save() {
 

        //开始提交

        $("#form").submit()

    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("添加成功！");
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>
