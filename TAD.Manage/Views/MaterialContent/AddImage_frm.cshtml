@{
    ViewBag.Title = "添加图片";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@using (Ajax.BeginForm("AddImage", "MaterialContent", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">图片：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <table>
                    <tr>
                        <td>
                            <div onclick="choosefile('thumbnail')" style="width:200px;height:150px;border:solid 1px #808080;  background-image:url('/Content/images/upLoadbg.png');background-repeat:no-repeat; border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%" id="thumbnail_upload" src="" />
                                <input hidden="hidden" id="thumbnail" name="thumbnail" value="" />
                            </div>
                        </td>
                    </tr>

                </table>
            </div>
        </div>
    </div>
    <input id="content_id" name="content_id" hidden="hidden" value="@ViewBag.content_id" />
}

<div class="site-demo-upbar" hidden="hidden">
    <input type="file" name="file" class="layui-upload-file" id="file">
</div>

<script>
    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
            , method: 'post' //上传接口的http类型
            , before: function (input) {
                show_loading();
            }
            , success: function (res) {
                close_loading();
                if (res.Isok) {
                    $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                    $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
                }
                else {
                    alert(res.Msg);
                }
            }
        });
    });

    var image_id;
    function choosefile(_thumbnail) {
        image_id = _thumbnail;
        var f = document.getElementById("file");
        f.click();
    }
</script>
<script>
    function Save() {

        //开始提交
        if (!isNullOrUndefined($("#thumbnail").val())) {
            $("#form").submit()
        }
        else {
            show_msg("图片不能为空。")
        }
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("添加成功！");
              window.location = "Enclosure_Index?content_id="+@ViewBag.content_id;
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>
