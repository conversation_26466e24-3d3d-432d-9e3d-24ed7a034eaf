@{
    ViewBag.Title = "编辑回复";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.material_content_attachment
@using (Ajax.BeginForm("EditCharacter", "MaterialContent", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">回复内容：</label>
        <div class="col-sm-9">
            <textarea id="tag_matter" name="tag_matter" style="width:650px;height:300px;">@Model.tag_matter</textarea>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="排序 是必需的。" name="sort_code" value="@Model.sort_code" placeholder="排序">
            <span class="field-validation-valid" data-valmsg-for="sort_code" data-valmsg-replace="true"></span>
        </div>
    </div>
    <input id="attachment_id" name="attachment_id" hidden="hidden" value="@Model.attachment_id" />

}
<script>
    function Save() {
        //开始提交
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("添加成功！");
            window.location = "Enclosure_Index?content_id="+@Model.content_id;
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>
