@{
    ViewBag.Title = "添加级别商品";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@using (Ajax.BeginForm("Add", "PartnerTypeCost", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type_id" id="type_id">
                <option value="0">请选择</option>
                @foreach (TAD.Model.partner_type item in (List<TAD.Model.partner_type>)(ViewData["partner_type"]))
                {
                    <option value="@item.type_id">@item.name</option>
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">产品：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="product_id" id="product_id">
                <option value="0">请选择</option>
                @foreach (TAD.Model.partner_card_product item in (List<TAD.Model.partner_card_product>)(ViewData["partner_card_product"]))
                {
                    <option value="@item.product_id">@item.name</option>
                }
            </select>
        </div>
    </div>



    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">数量：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="数量 是必需的。" name="amount" placeholder="请输入 数量">
            <span class="field-validation-valid" data-valmsg-for="rank" data-valmsg-replace="true"></span>
        </div>
    </div>


    @*<div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">折扣（%）：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="折扣 是必需的。" name="discount" placeholder="请输入 折扣">
            <span class="field-validation-valid" data-valmsg-for="discount" data-valmsg-replace="true"></span>
        </div>
    </div>*@
}
<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>


