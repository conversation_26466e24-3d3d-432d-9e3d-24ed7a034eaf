@{
    ViewBag.Title = "Edit_frm";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.partner_type_cost
@using (Ajax.BeginForm("Edit", "PartnerTypeCost", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">产品：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type_id" id="type_id">
                <option value="0">请选择</option>
                @foreach (TAD.Model.partner_type item in (List<TAD.Model.partner_type>)(ViewData["partner_type"]))
                {
                    if (Model.type_id == item.type_id)
                    {
                        <option selected="selected" value="@item.type_id">@item.name</option>
                    }
                    else
                    {
                        <option value="@item.type_id">@item.name</option>
                    }

                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">产品：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="product_id" id="product_id">
                <option value="0">请选择</option>
                @foreach (TAD.Model.partner_card_product item in (List<TAD.Model.partner_card_product>)(ViewData["partner_card_product"]))
                {
                    if (Model.product_id == item.product_id)
                    {
                        <option selected="selected" value="@item.product_id">@item.name</option>
                    }
                    else
                    {
                        <option value="@item.product_id">@item.name</option>
                    }
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">数量：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="数量 是必需的。" name="amount" value="@Model.amount" placeholder="请输入 数量">
            <span class="field-validation-valid" data-valmsg-for="rank" data-valmsg-replace="true"></span>
        </div>
    </div>

    @*<div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">折扣（%）：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="折扣 是必需的。" name="discount" placeholder="请输入 折扣" value="@Model.discount">
            <span class="field-validation-valid" data-valmsg-for="discount" data-valmsg-replace="true"></span>
        </div>
    </div>*@

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state == true)
                {
                    <option value="true" selected="selected">启用</option>
                    <option value="false">禁用</option>
                }
                else
                {
                    <option value="true">启用</option>
                    <option value="false" selected="selected">禁用</option>
                }
            </select>
        </div>
    </div>
    <input hidden="hidden" id="cost_id" name="cost_id" value="@Model.cost_id"/>
}
<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Index?time=" + new Date().toString();
        }
        else {
            show_msg("编辑失败" + data.Msg)
        }
    }

</script>



