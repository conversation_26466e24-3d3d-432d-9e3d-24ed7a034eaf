@{
    ViewBag.Title = "添加合伙人";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
<script>
    $(function () {
        $('#mobile_name').bind('input propertychange', function () {
            var mobile_name = $.trim($("#mobile_name").val());
            if (verify_mobile(mobile_name)) {

                ajaxRequest("LoadUser", "get", { mobile_name: mobile_name }, function (isok, ret) {
                    if (ret.Isok) {
                        $("#to_customer").css('display', 'block');
                        $("#to_customer_head_img").attr("src", ret.Obj.head_url);
                        $("#to_customer_name").text(ret.Obj.name);
                        $("#to_customer_mobile").text(ret.Obj.mobile);
                        $("#customer_id").val(ret.Obj.user_id);
                    }
                    else {
                        $("#to_customer").css('display', 'none');
                        $("#customer_id").val('');
                    }
                });
            }
            else {
                $("#to_customer").css('display', 'none');
                $("#customer_id").val('');
            }
        });
    })
</script>

@using (Ajax.BeginForm("Add", "SystRegionPartner", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <div class="col-sm-9">
            @Html.Action("SearchUser", "SharedLump")
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">合伙人类型：</label>
        <div class="col-sm-9">

            <select class="combobox form-control" name="type" id="type">
                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.syst_region_partner_type)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.syst_region_partner_type), myCode);//获取名称

                        <option value="@myCode">@strName</option>
                    }
                }
            </select>
        </div>
    </div>

    <div class="form-group" id="area_list">
        <label for="inputPassword" class="col-sm-2 control-label">地区：</label>
        <div class="col-sm-9">


            <div class="layui-inline">
                <select class="combobox form-control" name="province_id" id="province_id" style="width:150px;">

                    @foreach (TAD.Model.syst_province item in (List<TAD.Model.syst_province>)(ViewData["syst_province"]))
                    {
                        <option value="@item.province_id">@item.name</option>
                    }

                </select>

            </div>

            <div class="layui-inline">
                <select class="combobox form-control" name="city_id" id="city_id" style="width:150px;"></select>
            </div>
            <div class="layui-inline">
                <select class="combobox form-control" name="district" id="district" style="width:150px;"></select>
            </div>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">支付金额：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="支付金额 是必需的。" name="pay_money" placeholder="请输入 支付金额">
            <span class="field-validation-valid" data-valmsg-for="pay_money" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">生效日期：</label>
        <div class="col-sm-9">

            <div class="layui-inline">
                <input class="layui-input" placeholder="请选择 生效日期" data-val="true" data-val-required="生效日期 是必需的。" name="effective_date" onclick="layui.laydate({elem: this, istime: true, format: 'YYYY-MM-DD hh:mm:ss'})">
            </div>
            <span class="field-validation-valid" data-valmsg-for="effective_date" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="">

        </div>
    </div>

}

<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>

<script type="text/javascript">
    window.onload = function () {
        document.getElementById('province_id').addEventListener('change', function () {
            load_city(this.value);
        }, false);
        load_city($("#province_id").val());
        document.getElementById('city_id').addEventListener('change', function () {

            load_district(this.value);
        }, false);


    }
    function load_city(id) {
        ajaxRequest("GetCity", "get", { id: id }, function (isok, ret) {
            if (ret.Isok) {
                $("#city_id").html("");
                var city_html = "";
                var city_data = ret.Obj;
                for (var o in city_data) {
                    city_html = city_html + "      <option   value='" + city_data[o].city_id + "'>" + city_data[o].name + "</option>";
                }
                $("#city_id").html(city_html);
                load_district($("#city_id").val());
            }
        });
    }

    function load_district(id) {
        ajaxRequest("GetDistrict", "get", { id: id }, function (isok, ret) {
            if (ret.Isok) {
                $("#district").html("");
                var District_html = "";
                var District_data = ret.Obj;
                for (var o in District_data) {

                    District_html = District_html + "      <option   value='" + District_data[o].district_id + "'>" + District_data[o].name + "</option>";

                }
                $("#district").html(District_html);
            }
        });
    }

    $("#type").change(function () {
    
        var type = $("#type").val();
        if (type == 5) {
            $("#area_list").hide();
        } else {
            $("#area_list").show();
        }
    });
    layui.use('laydate', function () {
        var laydate = layui.laydate;
    });
</script>
