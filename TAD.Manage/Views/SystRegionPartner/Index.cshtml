@{
    ViewBag.Title = "区域合伙人";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('Add_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span>添加合伙人</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>


        </form>
    </div>
    <table style=" min-height:150px;" id="table"
           data-toggle="table"
           data-url="GetRolePage"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="name_mobile" data-sortable="true" data-formatter="UserHeadUrlFormatter">姓名</th>
                <th data-field="type_str" data-sortable="true">类型</th>
                <th data-field="pay_money" data-sortable="true" data-formatter="formatting_money">支付金额</th>
                <th data-field="province_name" data-sortable="true">省</th>
                <th data-field="city_name" data-sortable="true">市</th>
                <th data-field="district_name" data-sortable="true">区</th>
                <th data-field="state_str" data-sortable="true">状态</th>
                <th data-field="effective_date_str" data-formatter="formatting_date" data-sortable="true">生效日期</th>
                <th data-field="creation_date_str" data-formatter="formatting_date" data-sortable="true">创建时间</th>
                <th data-field="remark" data-sortable="true">备注说明</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
            </tr>
        </thead>
    </table>



</div>
<script type="text/javascript">

    function UserHeadUrlFormatter(value, row, index) {
        return "<div>  <table>   <tr><td style='width:65px; '>  <img style='width:60px; ' src='" + row.image_url + "' /> </td>      <td>    <div>" + row.user_name + "【" + row.mobile + "】</div>       <div> " + row.nickname + "  &nbsp; " + row.real_name + "&nbsp; </div>   </td>        </table></div>"
    }

    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);

    }
    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function customer_head_img(value, row, index) {
        return "<img style='width:60px; ' src='" + value + "' />"
    }

    function queryParams(params) {

        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),

        }
    }
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }

    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'
        html = html + '      <li><a href="#" onclick="Edit(' + row.partner_id + ')">编辑</a></li>'
        html = html + '      <li><a href="#" onclick="Delete(' + row.partner_id + ')">删除</a></li>'
        html = html + '      </ul>     </div>'
        return html;
    }
    //编辑
    function Edit(partner_id) {
        open_url("Edit_frm?partner_id=" + partner_id);
    }
    //删除
    function Delete(partner_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("Delete", "get", { partner_id: partner_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "index?time" + new Date().toString();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }

</script>

