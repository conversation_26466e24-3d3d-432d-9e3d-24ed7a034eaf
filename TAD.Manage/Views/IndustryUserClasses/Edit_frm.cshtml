@{
    ViewBag.Title = "编辑合伙人用户";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.industry_user_classes

@using (Ajax.BeginForm("Edit", "IndustryUserClasses", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">姓名：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" value="@ViewBag.name_mobile" readonly="readonly">
            <span class="field-validation-valid" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type_id" id="type_id">
                <option value="0">请选择</option>
                @foreach (TAD.Model.industry_type item in (List<TAD.Model.industry_type>)(ViewData["industry_type"]))
                {
                    if (Model.type_id == item.type_id)
                    {
                        <option selected="selected" value="@item.type_id">@item.name</option>
                    }
                    else
                    {
                        <option value="@item.type_id">@item.name</option>
                    }

                }
            </select>
        </div>
    </div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">支付金额：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="支付金额 是必需的。" name="pay_money" placeholder="请输入 支付金额" value="@Model.pay_money">
            <span class="field-validation-valid" data-valmsg-for="pay_money" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">支付方式：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="payment" id="payment">
                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.payment)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.payment), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        if (Model.payment == myCode)
                        {
                            <option selected="selected" value="@myCode">@strName</option>

                        }
                        else
                        {
                            <option value="@myCode">@strName</option>

                        }
                    }
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">保证金：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="保证金 是必需的。" name="guarantee_money" placeholder="请输入 保证金" value="@Model.guarantee_money">
            <span class="field-validation-valid" data-valmsg-for="guarantee_money" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state == true)
                {
                    <option value="true" selected="selected">启用</option>
                    <option value="false">禁用</option>
                }
                else
                {
                    <option value="true">启用</option>
                    <option value="false" selected="selected">禁用</option>
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入 备注信息" value="@Model.remark">
        </div>
    </div>
    <input value="@Model.classes_id" name="classes_id" id="classes_id" hidden="hidden" />
}
<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>


