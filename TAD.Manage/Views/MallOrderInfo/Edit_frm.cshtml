@{
    ViewBag.Title = "编辑订单";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.mall_order_info
@using (Ajax.BeginForm("Edit", "Order", new AjaxOptions { HttpMethod = "Post", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">购买人：</label>
        <div class="col-sm-9">
            <select onchange="selected_customer()" class="combobox form-control" name="customer_id" id="customer_id">

                @foreach (TAD.Model.syst_user item in (List<TAD.Model.syst_user>)(ViewData["syst_user"]))
                {

                    if (item.user_id == Model.user_id)
                    {
                        <option selected="selected" id="customer_id" value="@item.user_id">@item.user_name&nbsp;(@item.mobile)</option>
                    }
                    else
                    {
                        <option id="customer_id" value="@item.user_id">@item.user_name&nbsp;(@item.mobile)</option>
                    }


                }
            </select>

        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">推荐人：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="recommend_customer_id" id="recommend_customer_id">
                @foreach (TAD.Model.syst_user item in (List<TAD.Model.syst_user>)(ViewData["syst_user"]))
                {
                    if (item.user_id == Model.recommend_customer_id)
                    {
                        <option selected="selected" value="@item.user_id">@item.user_name&nbsp;(@item.mobile)</option>
                    }
                    else
                    {
                        <option value="@item.user_id">@item.user_name&nbsp;(@item.mobile)</option>
                    }

                }
            </select>

        </div>
    </div>

    @*<div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">订单产品：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="product_id" id="product_id">

                @foreach (TAD.Model.mall_product item in (List<TAD.Model.mall_product>)(ViewData["mall_product"]))
                {

                    string str_type = ((TAD.Model.Enum.mall_product_class)item.class_num).ToString();
                    if (item.product_id == Model.product_id)
                    {
                        <option selected="selected" value="@item.product_id">@item.name (@str_type)</option>
                    }
                    else
                    {
                        <option value="@item.product_id">@item.name (@str_type)</option>
                    }

                }
            </select>

        </div>
    </div>*@

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">销售单价：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="sales_price" data-val="true" data-val-required="销售单价 是必需的。" placeholder="请输入销售单价" value="@Model.sales_price">
            <span class="field-validation-valid" data-valmsg-for="sales_price" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">销售数量：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="product_count" data-val="true" data-val-required="销售数量 是必需的。" placeholder="请输入销售数量" value="@Model.product_count">
            <span class="field-validation-valid" data-valmsg-for="product_count" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">总金额：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" readonly="readonly" name="amount_price" placeholder="0" value="@Model.amount_price">

        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">收货人姓名：</label>
        <div class="col-sm-9">
            <input id="express_name" type="text" class="form-control" name="express_name" data-val="true" data-val-required="收货人姓名 是必需的。" placeholder="请输入 收货人姓名" value="@Model.express_name">
            <span class="field-validation-valid" data-valmsg-for="express_name" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">收货人电话：</label>
        <div class="col-sm-9">
            <input id="express_mobile" type="number" class="form-control" name="express_mobile" data-val="true" data-val-required="收货人电话 是必需的。" placeholder="请输入 收货人电话" value="@Model.express_mobile">
            <span class="field-validation-valid" data-valmsg-for="express_mobile" data-valmsg-replace="true"></span>
        </div>
    </div>

    //Html.RenderAction("Address", "ShareAddressControl");
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">选择地址：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <input onclick="" type="text" readonly="readonly" class="form-control" style=" width:100px;" data-val-required="省" name="express_province" id="province" placeholder="" value="@Model.express_province">
                <span class="field-validation-valid" data-valmsg-for="express_province" data-valmsg-replace="true"></span>
            </div>
            <div class="layui-inline">
                <input onclick="" type="text" readonly="readonly" class="form-control" style=" width:100px;" data-val-required="市" name="express_city" id="city" placeholder="" value="@Model.express_city">
                <span class="field-validation-valid" data-valmsg-for="express_city" data-valmsg-replace="true"></span>
            </div>
            <div class="layui-inline">
                <input onclick="" type="text" readonly="readonly" class="form-control" style=" width:100px;" data-val-required="区" name="express_district" id="district" placeholder="" value="@Model.express_district">
                <span class="field-validation-valid" data-valmsg-for="express_district" data-valmsg-replace="true"></span>
            </div>
            <div class="layui-inline">
                <input onclick="" type="text" class="form-control" style=" width:300px;" data-val-required="请选择地址" name="express_address" id="address" placeholder="" value="@Model.express_address">
                <span class="field-validation-valid" data-valmsg-for="express_address" data-valmsg-replace="true"></span>
            </div>
            <div class="layui-inline">
                <button type="button" class="btn btn-success" onclick="SelectAddress()" id="submitBTN"><span class="glyphicon  glyphicon-chevron-left"></span> 选择地址</button>
            </div>

        </div>

        <input hidden="hidden" name="let" id="let">
        <input hidden="hidden" name="lng" id="lng">
    </div>

    <div class="form-group">
        <label for="dtp_input1" class="col-md-2 control-label">订单日期：</label>
        <div style="padding-left:15px;" class="layui-inline">
            <input class="layui-input" name="creation_date" id="creation_date" data-val="true" data-val-required="订单日期 是必需的。" placeholder="请选择日期" onclick="layui.laydate({elem: this, istime: true, format: 'YYYY-MM-DD hh:mm:ss'})" value="@Model.creation_date">
            <span class="field-validation-valid" data-valmsg-for="creation_date" data-valmsg-replace="true"></span>
        </div>
    </div>
    <script type="text/javascript">
        $('.form_datetime').datetimepicker({
            language: 'zh-CN',
            format: 'yyyy-mm-dd hh:ii:ss',
            minuteStep: 1,
            autoclose: 1,
        });
    </script>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">备注说明：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" id="remark" placeholder="请输入备注说明" value="@Model.remark">
        </div>

    </div>
    <input hidden="hidden" name="info_id" value="@Model.info_id" />
}
<script>
    layui.use('laydate', function () {
        var laydate = layui.laydate;

        var start = {
            min: laydate.now()
          , max: '2099-06-16 23:59:59'
          , istoday: false
          , choose: function (datas) {
              end.min = datas; //开始日选好后，重置结束日的最小日期
              end.start = datas //将结束日的初始值设定为开始日
          }
        };

    });
</script>

<script>
    $(function () {
        $("input[name='sales_price']").keyup(calculate_sales_amount)
        $("input[name='product_count']").keyup(calculate_sales_amount)

        $("#customer_id").trigger("onchange");
    });

    function calculate_sales_amount() {
        var sales_price = parseInt($("input[name='sales_price']").val());
        var product_count = parseInt($("input[name='product_count']").val());
        var amount_price = sales_price * product_count;
        $("input[name='amount_price']").val(amount_price);
    }

    function Save() {
        $("#express_province").val($("#province").val());
        $("#express_city").val($("#city").val());
        $("#express_district").val($("#district").val());
        $("#express_address").val($("#address").val());
        $("#form").submit()
    }
    function OnSuccess(data) {
        if (data.Isok) {
            window.location = "/order/index?wq" + new Date().getSeconds();
        }
        else {
            alert("添加失败+" + data.Msg)
        }
    }





    function selected_customer() {
        ajaxRequest("GetExpressMessage", "post", { customer_id: $("#customer_id").val() }, function (isok, ret) {
            $("#express_name").val(ret.Obj.name);
            $("#express_mobile").val(ret.Obj.mobile);
        });


    }
    function SelectAddress() {


        var h = "430";
        layer.open({
            type: 2,

            yes: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']]; //得到iframe页的窗口对象，执行iframe页的方法：iframeWin.method();
                var html = iframeWin.getAddres();
                alert(JSON.stringify(html));
                layer.close(index);//需要手动关闭窗口
            },
            title: false,
            area: ['800px', '600px'],
            fixed: false, //不固定
            maxmin: true,
            content: '/CbdActivate/MapAddress_frm?h=' + h,
        });

    }
    function SelectAddressed(addres) {
        layer.closeAll('iframe');

        var lat = addres.location.lat;
        var lng = addres.location.lng;
        ajaxRequest("GetAddress", "get", { lat: lat, lng: lng }, function (isok, ret) {
            if (ret.Isok) {
                $("#let").val(lat)
                $("#lng").val(lng)
                $("#province").val(ret.Obj.regeocode.addressComponent.province);
                $("#city").val(ret.Obj.regeocode.addressComponent.city);
                $("#district").val(ret.Obj.regeocode.addressComponent.district);
                $("#address").val(addres.address + addres.name);
            }
        });

    }
</script>
<script>
    $(function () {
        $('#customer_id').combobox();
        $('#recommend_customer_id').combobox();

    });


</script>

