@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>


        </form>
    </div>
    <table style=" min-height:150px;" id="table"
           data-toggle="table"
           data-url="GetOrderInfoPage"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="name_mobile" data-sortable="true">姓名</th>
                <th data-field="mall_product_name" data-sortable="true">产品名称</th>
                <th data-field="property_name" data-sortable="true">产品规格</th>
                <th data-field="sales_price" data-formatter="formatting_money">销售价格</th>
                <th data-field="product_count" data-sortable="true">购买数量</th>
                <th data-field="amount_price" data-formatter="formatting_money">总价格</th>
                <th data-field="total_pay" data-formatter="formatting_money">支付总额</th>
                <th data-field="pay_type_str" data-sortable="true">支付方式</th>
                <th data-field="express_address" data-sortable="true">收货地址</th>
                <th data-field="state_str" data-sortable="true">状态</th>
                <th data-field="postscript" data-sortable="true">客户备注</th>
                <th data-field="remark" data-sortable="true">备注</th>
                <th data-field="creation_date_str" data-sortable="true" data-formatter="formatting_date">创建时间</th>
                <th data-field="customer_id" data-formatter="operation_menu" data-sortable="true" style="width:50%">操作</th>
            </tr>
        </thead>
    </table>
  


</div>
<script type="text/javascript">

    function UserHeadUrlFormatter(value, row, index) {
        return "<div>  <table>   <tr><td style='width:65px; '>  <img style='width:60px; ' src='" + row.image_url + "' /> </td>      <td>    <div>" + row.user_name + "【" + row.mobile + "】</div>       <div> " + row.nickname + "  &nbsp; " + row.real_name + "&nbsp; </div>   </td>        </table></div>"
    }

    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);

    }
    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function customer_head_img(value, row, index) {
        return "<img style='width:60px; ' src='" + value + "' />"
    }
    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'
        html = html + '<li><a href="#" onclick="Shipments(' + row.info_id + ')">发货</a></li>'
        html = html + '<li><a href="#" onclick="SetRemark(' + row.info_id + ')">备注</a></li>'
        html = html + '      </ul>     </div>'
        return html;
    }
    function queryParams(params) {

        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),

        }
    }
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }
     
    //发货
    function Shipments(info_id) {
        open_url("OrderExpreesIndex?info_id=" + info_id);
    }

    //备注说明
    function SetRemark(info_id) {
        show_prompt("输入管理员备注信息", '', function (value) {

            ajaxRequest("SetAdminRemark", "post", { info_id: info_id, value: value }, function (isok, ret) {
                if (isok) {
                    if (ret.Isok) {
                        window.location = "index?time" + new Date().toString();
                    }
                }
            });
        });
    }
</script>
