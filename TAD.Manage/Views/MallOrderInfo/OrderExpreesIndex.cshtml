@{
    ViewBag.Title = "订单发货";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@model TAD.Manage.Models.order_info_exprees
<div class="panel panel-default">
    <div class="panel-heading" style="text-align:center;">
        订单信息
    </div>
    <div class="panel-body">
        <div class="panel-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>姓名</th>
                            <th>手机号</th>
                            <th>所购产品</th>
                            <th>销售价格</th>
                            <th>数量</th>
                            <th>总价</th>
                            <th>状态</th>
                            <th>下单日期</th>
                            <th>备注说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>@Model.syst_user.user_name</td>
                            <td>@Model.syst_user.mobile</td>
                            <td>
                                @Model.mall_product.name (@Model.order_info.product_count 件)<br />
                                @*@Model.order_info.product_property*@
                            </td>
                            <td>@Model.order_info.sales_price.ToString("C")</td>
                            <td>@Model.order_info.product_count</td>
                            <td>@Model.order_info.amount_price.ToString("C")</td>
                            <td>
                                @{
                                    string state_str = ((TAD.Model.Enum.order_info_state)Model.order_info.state).ToString();
                                    <span>@state_str</span>
                                }
                            </td>
                            <td>@Model.order_info.creation_date.ToString()</td>
                            <td>@Model.order_info.remark</td>
                        </tr>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
</div>
<div class="panel panel-default">
    <div class="panel-heading" style="text-align:center;">
        发货记录
    </div>
    <div class="panel-body">
        <div class="panel-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>姓名</th>
                            <th>手机号</th>
                            <th>收货地址</th>
                            <th>发货产品</th>
                            <th>快递名称</th>
                            <th>快递单号</th>
                            <th>发货日期</th>
                            <th>备注说明</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>

                        @foreach (var item in Model.order_expreesList)
                        {
                            <tr>
                                <td>@item.recipients_name</td>
                                <td>@item.recipients_mobile</td>
                                <td>@item.recipients_address</td>
                                <td>@item.product_info</td>
                                <td>@item.express_company</td>
                                <td>@item.express_num</td>
                                <td>@item.creation_date.ToShortDateString()</td>
                                <td>@item.remark</td>
                                <td>

                                    <div class="btn-toolbar" role="toolbar">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-default" onclick="edit('@item.order_express_id')">编辑</button>

                                        </div>
                                    </div>

                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>



<div class="panel panel-default">
    <div class="panel-heading" style="text-align:center;">
        发货信息
    </div>


    @if (ViewData["order_exprees"] == null)
    {
        using (Ajax.BeginForm("AddExprees", "MallOrderInfo", new AjaxOptions { HttpMethod = "Post", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
        {
            <input hidden="hidden" name="user_id" value="@Model.order_info.user_id" />
            <input hidden="hidden" name="order_id" value="@Model.order_info.info_id" />
            <div class="panel-body">
                <form class="form-horizontal" role="form">
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">发货产品：</label>
                        <div class="col-sm-10">
                            <input type="text" name="product_info" class="form-control" placeholder="请输入所发货的产品信息" value="@Model.mall_product.name">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">收件人姓名：</label>
                        <div class="col-sm-10">
                            @if (string.IsNullOrEmpty(Model.order_info.express_name))
                            {
                                <input type="text" name="recipients_name" class="form-control" placeholder="请输入收件人姓名" value="@Model.syst_user.user_name">
                            }
                            else
                            {
                                <input type="text" name="recipients_name" class="form-control" placeholder="请输入收件人姓名" value="@Model.order_info.express_name">
                            }

                        </div>
                    </div>
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">收件人电话：</label>
                        <div class="col-sm-10">
                            @if (string.IsNullOrEmpty(Model.order_info.express_mobile))
                            {
                                <input type="text" name="recipients_mobile" class="form-control" placeholder="请输入收件人电话" value="@Model.order_info.express_mobile">
                            }
                            else
                            {
                                <input type="text" name="recipients_mobile" class="form-control" placeholder="请输入收件人电话" value="@Model.order_info.express_mobile">
                            }
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">收件人地址：</label>
                        <div class="col-sm-10">
                            <input type="text" name="recipients_address" class="form-control" placeholder="请输入收件人地址" value="@Model.order_info.express_province @Model.order_info.express_city @Model.order_info.express_district  @Model.order_info.express_address ">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="lastname" class="col-sm-2 control-label">物流公司：</label>
                        <div class="col-sm-10">
                            <select class="combobox form-control" name="express_company_code" id="express_company_code">
                                @if (Model.mall_product.kind == 1)
                                {
                                    <option value="">无需物流</option>
                                }


                                @foreach (TAD.Utility.ServerAPI.Models.ExpressCode itemlist in (List<TAD.Utility.ServerAPI.Models.ExpressCode>)(ViewData["Express_code"]))
                                {

                                    <option value="@itemlist.Code">@itemlist.Name</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">物流单号：</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" data-valmsg-for="express_num" placeholder="请输入物流单号" name="express_num">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">备注信息：</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" placeholder="备注信息" name="remark" value="">
                        </div>
                    </div>
                </form>
            </div>
        }
    }
    else
    {
        using (Ajax.BeginForm("EditExprees", "Order", new AjaxOptions { HttpMethod = "Post", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
        {
            //编辑
            TAD.Model.order_exprees order_exprees = (TAD.Model.order_exprees)ViewData["order_exprees"];

            <input hidden="hidden" name="order_express_id" value="@order_exprees.order_express_id" />
            <div class="panel-body">
                <form class="form-horizontal" role="form">
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">发货产品：</label>
                        <div class="col-sm-10">
                            <input type="text" name="product_info" class="form-control" placeholder="请输入所发货的产品信息" value="@order_exprees.product_info">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">收件人姓名：</label>
                        <div class="col-sm-10">
                            <input type="text" name="recipients_name" class="form-control" placeholder="请输入收件人姓名" value="@order_exprees.recipients_name">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">收件人电话：</label>
                        <div class="col-sm-10">
                            <input type="text" name="recipients_mobile" class="form-control" placeholder="请输入收件人电话" value="@order_exprees.recipients_mobile">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">收件人地址：</label>
                        <div class="col-sm-10">
                            <input type="text" name="recipients_address" class="form-control" placeholder="请输入收件人地址" value="@order_exprees.recipients_address">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="lastname" class="col-sm-2 control-label">物流公司：</label>
                        <div class="col-sm-10">
                            <select class="combobox form-control" name="express_company_code" id="express_company_code">
                                <option value="">无需物流</option>
                                @foreach (TAD.Utility.ServerAPI.Models.ExpressCode itemlist in (List<TAD.Utility.ServerAPI.Models.ExpressCode>)(ViewData["Express_code"]))
                                {
                                    if (itemlist.Code == order_exprees.express_company_code)
                                    {
                                        <option selected="selected" value="@itemlist.Code">@itemlist.Name</option>
                                    }
                                    else
                                    {
                                        <option value="@itemlist.Code">@itemlist.Name</option>
                                    }

                                }
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">物流单号：</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" data-valmsg-for="express_num" placeholder="请输入物流单号" name="express_num" value="@order_exprees.express_num">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="firstname" class="col-sm-2 control-label">备注信息：</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" placeholder="备注信息" name="remark" value="@order_exprees.remark">
                        </div>
                    </div>

                </form>
            </div>
        }
    }
</div>
<script>
    function Save() {
        $("#form").submit()

    }
    function OnSuccess(data) {
        show_loading();
        if (data.Isok) {
            self.location = document.referrer;
        }
        else {
            close_loading();
            alert("发货失败:" + data.Msg)
        }
    }
    function edit(order_express_id) {
        window.location = "OrderExpreesIndex?order_express_id=" + order_express_id +"&info_id="+@Model.order_info.info_id;
    }
</script>
<script>
    $(function () {
        $('#express_company_code').combobox()
    });
</script>