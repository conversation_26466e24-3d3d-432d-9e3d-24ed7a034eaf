@{
    ViewBag.Title = "回复内容";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model List<TAD.Manage.Models.SystSuggestMsg>
@*<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button style="margin-right:5px;" type="button" class="btn btn-success" onclick="javascript: history.back(-1);" id="submitBTN"><span class="glyphicon  glyphicon-list-alt"></span>返回</button>
                    </td>
                    <td style="width:10px;"></td>
                </tr>
            </table>
        </form>
    </div>
</div>*@

<table style="width:50%;height:400px; float:left" class="table table-bordered table-hover">
    @if (Model.Count > 0)
    {
        foreach (var item in Model)
        {
            if (item.send_user.user_id == (int)ViewBag.user_id)
            {
                <tr>
                    <td>
                        <div>
                            <span>@item.send_user.user_name @item.syst_user_suggest_msg.creation_date</span>

                        </div>
                        <div style="font-size:16px;">@item.syst_user_suggest_msg.message</div>
                    </td>

                </tr>
            }
            else
            {
                <tr>
                    <td style="margin-bottom:10px;">
                        <div>
                            <span>@item.recevie_user.user_name @item.syst_user_suggest_msg.creation_date</span>
                        </div>
                        <div style="font-size:16px;font-weight:bold">
                            @item.syst_user_suggest_msg.message
                        </div>
                    </td>
                </tr>
            }
        }
    }

</table>

<div style="float:right;width:50%;">

    <div style="text-align:center;font-size:20px;">回复客户内容</div>

    <div class="form-group">
        <label class="col-sm-2 control-label">请输入回复内容：</label>
        <div class="col-sm-9">


            <textarea id="content" style="width:600px;height:400px;"></textarea>

        </div>
    </div>
</div>
@*<button style="margin-right:5px;margin-top:30px;margin-left:700px;" type="button" class="btn btn-success" onclick="Save()" id="submitBTN"><span class="glyphicon  glyphicon-list-alt"></span>确定</button>*@

<script type="text/javascript">
    function Save() {
        var content = $("#content").val();
        var send_user_id = getUrlParam("send_user_id");
        if (isNullOrUndefined(content)) {
            alert("回复内容不能为空！");
            return;
        } else {
            ajaxRequest("ReplyCustomer", "post", { send_user_id: send_user_id, content: content }, function (isok, ret) {
                if (isok) {
                    if (ret.Isok) {
                        window.location = "/MessageSuggest/Index?wq" + new Date().getSeconds();
                    } else {
                        alert("回复失败！")
                    }
                }
            });
        }

    }


</script>

