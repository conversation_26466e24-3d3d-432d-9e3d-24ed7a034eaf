@{
    ViewBag.Title = "意见反馈";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

@model List<TAD.Manage.Models.SystSuggestMsg>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <div class="panel-body">
        <div class="table-responsive" style="padding:0px; margin:0px;">
            <table class="table table-bordered table-hover" style="padding:0px; margin:0px;">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>客户姓名</th>
                        <th>回复内容</th>
                        <th>创建日期</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="search_table">
                    @if (Model != null && Model.Count > 0)
                    {
                        int rowIndex = 0;
                        foreach (var item in Model)
                        {

                            rowIndex++;
                            <tr>
                                <td>@rowIndex</td>
                                <td>
                                    <div>  <table>   <tr><td style=width:65px;>  <img style=width:60px; src=" @item.send_user.head_img " /> </td>      <td>    <div>@item.send_user.user_name 【 @item.send_user.mobile】</div>       <div>@item.send_user.nickname &nbsp; @item.send_user.real_name &nbsp; </div>      </td>        </table></div>




                                </td>
                                <td>@item.syst_user_suggest_msg.message</td>
                                <td>@item.syst_user_suggest_msg.creation_date</td>

                                <td>
                                    @if (item.send_user.user_id != ViewBag.user_id)
                                    {
                                        <button class="layui-btn layui-btn-normal" onclick="Reply('@item.syst_user_suggest_msg.send_user_id')">回复</button>

                                    }
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>

    </div>
</div>

<script type="text/javascript">

    //回复
    function Reply(send_user_id) {
        open_url('Reply_frm?send_user_id=' + send_user_id);
    }

</script>
