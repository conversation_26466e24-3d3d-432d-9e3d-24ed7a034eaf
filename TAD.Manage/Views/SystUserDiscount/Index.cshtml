@{
    ViewBag.Title = "优惠券管理";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}


<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('Add_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span>添加优惠券</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <div class="table-responsive">
                <table style=" min-height:300px;" id="table"
                       data-toggle="table"
                       data-url="GetDiscounttList"
                       data-page-size="50"
                       data-side-pagination="server"
                       data-toolbar="#toolbar"
                       data-pagination="true"
                       data-page-list="[50, 100,150]"
                       data-cookie="true"
                       data-cookie-id-table="saveId"
                       data-query-params="queryParams">
                    <thead>
                        <tr>
                            <th data-field="index" data-formatter="indexFormatter" data-width="20">序号</th>
                            <th data-field="name_mobile">会员姓名</th>
                            <th data-field="tag_type_str">优惠券类型</th>
                            <th data-field="money" data-formatter="formatting_money">优惠券金额</th>
                            <th data-field="user_money_min" data-formatter="formatting_money">满足金额可用</th>
                            <th data-field="state_str">状态</th>
                            <th data-field="datetime_str">开始/结束时间</th>
                            <th data-field="explain">说明</th>
                            <th data-field="remark">备注</th>
                            <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">

    function formatting_money(value, row, index) {
        return formatCurrency(value);
    }
    function formattion_start_date(value, row, index) {

        return formatDate(value);
    }
    function imageFormatter(value, row, index) {
        if (value == null) {
            value = "";
        }
        return "<img style='width:60px;' src='" + value + "' />"
    }

    function indexFormatter(value, row, index) {
        return index + 1;
    }

    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }


    function queryParams(params) {
        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val()
        }
    }
    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'
        html = html + '      <li><a href="#" onclick="Edit(' + row.discount_id + ')">编辑</a></li>'
        html = html + '      <li><a href="#" onclick="Delete(' + row.discount_id + ')">删除</a></li>'
        html = html + '      </ul>     </div>'
        return html;
    }
    //编辑
    function Edit(discount_id) {
        open_url("/SystUserDiscount/Edit_frm?discount_id=" + discount_id)
    }

    //删除
    function Delete(discount_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("Delete", "get", { discount_id: discount_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "Index?time" + new Date().toString();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }



</script>
