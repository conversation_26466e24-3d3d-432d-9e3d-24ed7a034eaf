@{
    ViewBag.Title = "添加优惠券";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

<script>
    $(function () {
        $('#mobile_name').bind('input propertychange', function () {
            var mobile_name = $.trim($("#mobile_name").val());
            if (verify_mobile(mobile_name)) {

                ajaxRequest("LoadUser", "get", { mobile_name: mobile_name }, function (isok, ret) {
                    if (ret.Isok) {
                        $("#to_customer").css('display', 'block');
                        $("#to_customer_head_img").attr("src", ret.Obj.head_url);
                        $("#to_customer_name").text(ret.Obj.name);
                        $("#to_customer_mobile").text(ret.Obj.mobile);
                        $("#customer_id").val(ret.Obj.user_id);
                    }
                    else {
                        $("#to_customer").css('display', 'none');
                        $("#customer_id").val('');
                    }
                });
            }
            else {
                $("#to_customer").css('display', 'none');
                $("#customer_id").val('');
            }
        });
    })
</script>
@using (Ajax.BeginForm("Add", "SystUserDiscount", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <div class="col-sm-9">
            @Html.Action("SearchUser", "SharedLump")
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">优惠券类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="tag_type" id="tag_type">

                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.syst_user_discount_tag_type)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.syst_user_discount_tag_type), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值

                        <option value="@myCode">@strName</option>
                    }
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">优惠券金额：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="优惠券金额 是必需的。" name="money" placeholder="请输入 优惠券金额">
            <span class="field-validation-valid" data-valmsg-for="money" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">满足金额可用：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="满足金额可用 是必需的。" value="0" name="user_money_min" placeholder="请输入 满足金额可用">
            <span class="field-validation-valid" data-valmsg-for="user_money_min" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">目标ID：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="tag_id" value="1">
            <span class="field-validation-valid" data-valmsg-for="tag_id" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">开始时间：</label>
        <div class="col-sm-9">

            <div class="layui-inline">
                <input class="layui-input" placeholder="请选择 开始时间"  name="start_datetime" onclick="layui.laydate({elem: this, istime: true, format: 'YYYY-MM-DD hh:mm:ss'})">
            </div>
            <span class="field-validation-valid" data-valmsg-for="start_datetime" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">结束时间：</label>
        <div class="col-sm-9">

            <div class="layui-inline">
                <input class="layui-input" placeholder="请选择 结束时间"  name="finish_datetime" onclick="layui.laydate({elem: this, istime: true, format: 'YYYY-MM-DD hh:mm:ss'})">
            </div>
            <span class="field-validation-valid" data-valmsg-for="finish_datetime" data-valmsg-replace="true"></span>

        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">说明：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="explain" placeholder="请输入 说明">
            <span class="field-validation-valid" data-valmsg-for="explain" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="备注 是必需的。" name="remark" placeholder="请输入 备注">
            <span class="field-validation-valid" data-valmsg-for="remark" data-valmsg-replace="true"></span>
        </div>
    </div>

}


<script>
    function Save() {
        //开始提交
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
    layui.use('laydate', function () {
        var laydate = layui.laydate;
    });
</script>

