@{
    ViewBag.Title = "编辑优惠券";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.syst_user_discount
@using (Ajax.BeginForm("Edit", "SystUserDiscount", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">用户姓名：</label>
        <div class="col-sm-9">
            <input type="text" readonly="readonly" class="form-control"value="@ViewBag.name_mobil">
            <span class="field-validation-valid"  data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">优惠券类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="tag_type" id="tag_type">

                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.syst_user_discount_tag_type)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.syst_user_discount_tag_type), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        if (Model.tag_type == myCode)
                        {
                            <option selected="selected" value="@strVaule">@strName</option>
                        }
                        else
                        {
                            <option value="@strVaule">@strName</option>
                        }
                    }
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">优惠券金额：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="优惠券金额 是必需的。" name="money" placeholder="请输入 优惠券金额" value="@Model.money">
            <span class="field-validation-valid" data-valmsg-for="money" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">满足金额可用：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="满足金额可用 是必需的。" value="@Model.user_money_min" name="user_money_min" placeholder="请输入 满足金额可用">
            <span class="field-validation-valid" data-valmsg-for="user_money_min" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">目标ID：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="tag_id" value="@Model.tag_id">
            <span class="field-validation-valid" data-valmsg-for="tag_id" data-valmsg-replace="true"></span>
        </div>
    </div>


    <div class="form-group">
        <label class="col-sm-2 control-label">开始时间：</label>
        <div class="col-sm-9">

            <div class="layui-inline">
                <input class="layui-input" placeholder="开始时间" data-val="true" data-val-required="开始时间 是必需的。" name="start_datetime" onclick="layui.laydate({elem: this, istime: true, format: 'YYYY-MM-DD hh:mm:ss'})" value="@Model.start_datetime">
            </div>
            <span class="field-validation-valid" data-valmsg-for="start_datetime" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">结束时间：</label>
        <div class="col-sm-9">

            <div class="layui-inline">
                <input class="layui-input" placeholder="请选择课程结束时间" data-val="true" data-val-required="结束时间 是必需的。" name="finish_datetime" onclick="layui.laydate({elem: this, istime: true, format: 'YYYY-MM-DD hh:mm:ss'})" value="@Model.finish_datetime">
            </div>
            <span class="field-validation-valid" data-valmsg-for="finish_datetime" data-valmsg-replace="true"></span>

        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state == true)
                {
                    <option selected="selected" value="true">可用</option>
                    <option value="false">禁用</option>
                }
                else
                {
                    <option  value="true">可用</option>
                    <option selected="selected"  value="false">禁用</option>
                }

            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">说明：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="explain" placeholder="请输入 说明" value="@Model.explain">
            <span class="field-validation-valid" data-valmsg-for="explain" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="备注 是必需的。" value="@Model.remark" name="remark" placeholder="请输入 备注">
            <span class="field-validation-valid" data-valmsg-for="remark" data-valmsg-replace="true"></span>
        </div>
    </div>

    <input hidden="hidden" value="@Model.discount_id" name="discount_id" id="discount_id" />
}


<script>
    function Save() {
        //开始提交
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("修改失败" + data.Msg)
        }
    }
    layui.use('laydate', function () {
        var laydate = layui.laydate;
    });
</script>

