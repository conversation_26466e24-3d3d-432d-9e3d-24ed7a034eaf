@{
    ViewBag.Title = "添加提成配置";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@using (Ajax.BeginForm("Add", "SystParentCommission", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <div class="col-sm-9">
            @Html.Action("SearchUser", "SharedLump")
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type_id" id="type_id">
                <option value="0">请选择</option>
                @foreach (TAD.Model.partner_type item in (List<TAD.Model.partner_type>)(ViewData["partner_type"]))
                {
                    <option value="@item.type_id">@item.name</option>
                }
            </select>
        </div>
    </div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">配置类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="tag_type" id="tag_type">

                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.partner_commission_config_tag_type)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.partner_commission_config_tag_type), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        <option value="@myCode">@strName</option>
                    }
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">配置比例（%）：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="value" placeholder="请输入 配置比例" data-val="true" data-val-required="配置比例 是必需的。">
            <span class="field-validation-valid" data-valmsg-for="value" data-valmsg-replace="true"></span>
        </div>
    </div>


}

<script>
    function Save() {
        //开始提交
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>