@{
    ViewBag.Title = "编辑提成配置";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.partner_commission_config

@using (Ajax.BeginForm("Edit", "SystParentCommission", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    if (Model.user_id<=0)
    {
        <div class="form-group">
            <div class="col-sm-9">
                @Html.Action("SearchUser", "SharedLump")
            </div>
        </div>
    }
    else
    {
        <div class="form-group">
            <label for="inputPassword" class="col-sm-2 control-label">用户：</label>
            <div class="col-sm-9">
                <input readonly="readonly" type="text" class="form-control" value="@ViewBag.name_mobile">
            </div>
        </div>
    }

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type_id" id="type_id">
                <option value="0">请选择</option>
                @foreach (TAD.Model.partner_type item in (List<TAD.Model.partner_type>)(ViewData["partner_type"]))
                {
                    if (Model.type_id == item.type_id)
                    {
                        <option selected="selected" value="@item.type_id">@item.name</option>
                    }
                    else
                    {
                        <option value="@item.type_id">@item.name</option>
                    }

                }
            </select>
        </div>
    </div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">配置类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="tag_type" id="tag_type">

                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.partner_commission_config_tag_type)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.partner_commission_config_tag_type), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        if (Model.tag_type == myCode)
                        {
                            <option selected="selected" value="@myCode">@strName</option>
                        }
                        else
                        {
                            <option value="@myCode">@strName</option>
                        }

                    }
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">配置比例（%）：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="value" value="@Model.value" placeholder="请输入 配置比例" data-val="true" data-val-required="配置比例 是必需的。">
            <span class="field-validation-valid" data-valmsg-for="value" data-valmsg-replace="true"></span>
        </div>
    </div>

    <input  hidden="hidden" id="config_id" value="@Model.config_id" name="config_id"/>

}

<script>
    function Save() {
        //开始提交
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>