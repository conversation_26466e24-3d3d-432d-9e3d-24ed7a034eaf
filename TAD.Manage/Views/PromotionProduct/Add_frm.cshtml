@{
    ViewBag.Title = "添加抵扣券";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@using (Ajax.BeginForm("Add", "PromotionProduct", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <label class="col-sm-2 control-label">优惠券名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="name" placeholder="请输入 优惠券名称" data-val="true" data-val-required="优惠券名称 是必需的。">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">优惠券类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type" id="type">

                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.promotion_product_type)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.promotion_product_type), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        <option value="@myCode">@strName</option>
                    }
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">优惠券金额：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="type_value" placeholder="请输入 优惠券金额" data-val="true" data-val-required="优惠券金额 是必需的。">
            <span class="field-validation-valid" data-valmsg-for="type_value" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="sort" placeholder="请输入 排序" data-val="true" value="1" data-val-required="排序 是必需的。">
            <span class="field-validation-valid" data-valmsg-for="sort" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">备注说明：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入 备注说明" >
            <span class="field-validation-valid" data-valmsg-for="remark" data-valmsg-replace="true"></span>
        </div>
    </div>

}

<script>
    function Save() {
        //开始提交
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>