@{
    ViewBag.Title = "抵扣券管理";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('Add_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                    <td></td>
                </tr>
            </table>
        </form>
    </div>

    <table style=" min-height:100px;" id="table"
           data-toggle="table"
           data-url="GetPromotionPageData"
           data-page-size="10"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="name">优惠券名称</th>
                <th data-field="type_str">类型</th>
                <th data-field="type_value">优惠券金额</th>
                <th data-field="state_str">状态</th>
                <th data-field="sort">排序</th>
                <th data-field="remark">备注</th>
                <th data-field="creation_date_str" data-formatter="formatting_date">创建日期</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
            </tr>
        </thead>
    </table>

</div>
<script type="text/javascript">
    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);
    }

    function UserHeadUrlFormatter(value, row, index) {
        return "<div>  <table>   <tr><td style='width:65px; '>   <img style='width:60px; ' src='" + row.head_img_url + "' /> </td>      <td>    <div>" + row.user_name + "【" + row.mobile + "】</div>       <div> " + row.nickname + "  &nbsp; " + row.real_name + "&nbsp; " + row.sex + " </div>        </td>    </tr>    </table></div>"
    }


    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'
        html = html + '   <li><a href="#" onclick="Edit(' + row.product_id + ')">编辑</a></li>'
        html = html + '   <li><a href="#" onclick="Delete(' + row.product_id + ')">删除</a></li>'
        html = html + '      </ul>     </div>'
        return html;
    }

    function queryParams(params) {
        var key = $.trim($('#search_text').val())
        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: key,

        }
    }
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');

    }

    function Edit(product_id) {

        open_url("Edit_frm?product_id=" + product_id);
    }
    //删除
    function Delete(product_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("Delete", "get", { product_id: product_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "index?time" + new Date().toString();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }

</script>


