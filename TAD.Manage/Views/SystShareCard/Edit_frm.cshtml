@{
    ViewBag.Title = "Edit_frm";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@model TAD.Model.syst_share_card

@using (Ajax.BeginForm("Edit", "StudentCase", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">背景图片：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <table>
                    <tr>
                        <td>
                            <div onclick="choosefile('image_url')" style="width:195px ;height:348px;  background-image:url('/Content/images/upLoadbg.png');background-repeat:no-repeat; border:1px solid #c3c2c2;border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%;" id="image_url_upload" src="@Model.image_url" />
                                <input hidden="hidden" id="image_url" name="image_url" value="@Model.image_url" />
                            </div>
                        </td>
                    </tr>

                </table>
            </div>
            <div class="layui-inline">
                说明：750px*1334px像素的图片
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">缩略图：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <table>
                    <tr>
                        <td>
                            <div onclick="choosefile('thumbnail_url')" style="width:134px ;height:135px;  background-image:url('../Content/images/upLoadbg.png');background-repeat:no-repeat; border:1px solid #c3c2c2;border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%;" id="thumbnail_url_upload" src="@Model.thumbnail_url" />
                                <input hidden="hidden" id="thumbnail_url" name="thumbnail_url" value="@Model.thumbnail_url" />
                            </div>

                        </td>
                    </tr>

                </table>
            </div>
            <div class="layui-inline">
                说明：132px*132px像素的图片
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">课程分类：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <select class="combobox form-control" name="tag_type" id="tag_type">
                    @foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.syst_share_card_tag_type)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.syst_share_card_tag_type), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值

                        if (Model.tag_type == myCode)
                        {
                            <option selected="selected" value="@strVaule">@strName</option>
                        }
                        else
                        {
                            <option value="@strVaule">@strName</option>
                        }

                    }
                </select>
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">头像X轴：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="head_portrait_x" placeholder="请输入 头像X轴数值" data-val="true" data-val-required="头像X轴数值 是必需的。" value="@Model.head_portrait_x">
            <span class="field-validation-valid" data-valmsg-for="head_portrait_x" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">头像Y轴：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="head_portrait_y" placeholder="请输入 头像Y轴数值" data-val="true" data-val-required="头像Y轴数值 是必需的。" value="@Model.head_portrait_y">
            <span class="field-validation-valid" data-valmsg-for="head_portrait_y" data-valmsg-replace="true"></span>
        </div>
    </div>


    <div class="form-group">
        <label class="col-sm-2 control-label">二维码X轴：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="qrcode_x" placeholder="请输入 二维码X轴数值" data-val="true" data-val-required="二维码X轴数值 是必需的。" value="@Model.qrcode_x">
            <span class="field-validation-valid" data-valmsg-for="qrcode_x" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">二维码Y轴：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="qrcode_y" placeholder="请输入 二维码Y轴数值" data-val="true" data-val-required="二维码Y轴数值 是必需的。" value="@Model.qrcode_y">
            <span class="field-validation-valid" data-valmsg-for="qrcode_y" data-valmsg-replace="true"></span>
        </div>
    </div>


    <div class="form-group">
        <label class="col-sm-2 control-label">封面X轴：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="cover_x" placeholder="请输入 封面X轴数值" data-val="true" data-val-required="封面X轴数值 是必需的。" value="@Model.cover_x">
            <span class="field-validation-valid" data-valmsg-for="cover_x" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">封面Y轴：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="cover_y" placeholder="请输入 封面Y轴数值" data-val="true" data-val-required="封面Y轴数值 是必需的。" value="@Model.cover_y">
            <span class="field-validation-valid" data-valmsg-for="cover_y" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">标题X轴：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="title_x" placeholder="请输入 标题X轴数值" data-val="true" data-val-required="标题X轴数值 是必需的。" value="@Model.title_x">
            <span class="field-validation-valid" data-valmsg-for="title_x" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">标题Y轴：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="title_y" placeholder="请输入 标题Y轴数值" data-val="true" data-val-required="标题Y轴数值 是必需的。" value="@Model.title_y">
            <span class="field-validation-valid" data-valmsg-for="title_y" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">颜色管理：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" value="@Model.colour_name" name="colour_name" data-val="true" data-val-required="颜色管理 是必需的。" placeholder="请输入 颜色标识">
            <span class="field-validation-valid" data-valmsg-for="colour_name" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">序号：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" value="@Model.sort_code" name="sort_code" data-val="true" data-val-required="序号 是必需的。" placeholder="请输入 序号">
            <span class="field-validation-valid" data-valmsg-for="sort_code" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state == 1)
                {
                    <option value="1" selected="selected">可用</option>
                    <option value="0">禁用</option>
                }
                else
                {
                    <option value="1">可用</option>
                    <option value="0" selected="selected">禁用</option>
                }
            </select>
        </div>
    </div>
    <input type="number" value="@Model.card_id" id="card_id" name="card_id" hidden="hidden" />
}
<div class="site-demo-upbar" hidden="hidden">
    <input type="file" name="file" class="layui-upload-file" id="file">
</div>

<script>
    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
            , method: 'post' //上传接口的http类型
            , before: function (input) {
                show_loading();
            }
            , success: function (res) {
                close_loading();
                if (res.Isok) {
                    $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                    $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
                }
                else {
                    alert(res.Msg);
                }
            }
        });
    });

    var image_id;
    function choosefile(_image_id) {
        image_id = _image_id;
        var f = document.getElementById("file");
        f.click();
    }
</script>

<script>
    function Save() {
        //开始提交
        if (!isNullOrUndefined($("#image_url").val()) && !isNullOrUndefined($("#thumbnail_url").val())) {
            $("#form").submit()
        } else {
            show_msg("图片不能为空。")
        }

    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

    $("#course_id").change(function () {
        var course_id = $("#course_id").val();
        ajaxRequest("GetSchouldContent", "Get", { course_id: course_id }, function (isok, ret) {
            if (ret.Isok) {
                var introduction = UE.getEditor('editor').setContent(ret.Obj, false);
                $("#introduce").val(introduction);

            }

        });
    });
</script>

<script>
    layui.use('laydate', function () {
        var laydate = layui.laydate;
    });
</script>
