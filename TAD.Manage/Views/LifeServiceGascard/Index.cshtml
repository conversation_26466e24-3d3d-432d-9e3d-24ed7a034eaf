@{
    ViewBag.Title = "油卡产品";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('Add_frm')" id="submitBTN"><span class="glyphicon  glyphicon-plus"></span> 添加商品</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>

    <table style=" min-height:100px;" id="table"
           data-toggle="table"
           data-url="GetTopupGascard_page"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter" data-width="20">序号</th>
                <th data-field="name">商品名称</th>
                <th data-field="company_name">运营商</th>
                <th data-field="sell_price" data-formatter="formatting_money">销售价格</th>
                <th data-field="stock_price" data-formatter="formatting_money">进货价格</th>
                <th data-field="inventory">库存</th>
                <th data-field="sales_volume">销量</th>
                <th data-field="state_str">状态</th>
                <th data-field="creation_date_str" data-formatter="formatting_date">创建日期</th>
                <th data-field="update_date_str" data-formatter="formatting_date">更新日期</th>
                <th data-field="remark">备注说明</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
            </tr>
        </thead>
    </table>
</div>


@*分页js*@
<script type="text/javascript">
    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);
    }

    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function queryParams(params) {

        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),
        }
    }

    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }

    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'

        html = html + "<li onclick=\"Edit('" + row.gascard_id + "' )\"> <a href='#'>编辑</a></li>"



        html = html + '      </ul>     </div>'
        return html;
    }

    function Edit(gascard_id) {
        open_url('Edit_frm?gascard_id=' + gascard_id)
    }
</script>