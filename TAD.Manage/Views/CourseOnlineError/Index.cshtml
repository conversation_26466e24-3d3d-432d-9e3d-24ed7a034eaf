@{
    ViewBag.Title = "课程报障";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";

}
<div id=""></div>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    @*<td>
                            <button type="button" class="btn btn-success" onclick="open_url('Add_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加</button>
                        </td>*@
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <table style=" min-height:150px;" id="table"
           data-toggle="table"
           data-url="GetCourseOnlineErrorPage"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="syst_user_name" data-sortable="true">报障人</th>
                <th data-field="syst_user_mobile" data-sortable="true">报障人电话</th>
                <th data-field="online_name" data-sortable="true">故障课程</th>
                <th data-field="online_list_str" data-sortable="true">故障节数</th>
                <th data-field="explain_name_str" data-sortable="true">故障内容</th>
                <th data-field="image_url" data-sortable="true" data-formatter="image_url">故障截图</th>
                <th data-field="state_str" data-sortable="true">状态</th>
                <th data-field="creation_date_str" data-formatter="formatting_date" data-sortable="true">创建时间</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
            </tr>
        </thead>
    </table>
</div>
<script type="text/javascript">
    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function image_url(value, row, index) {
        return "<img  id=\"image_url\" style='width:60px;' src='" + value + "' onclick=\"open_image('" + row.image_url + "')\" />"
    }

    function queryParams(params) {

        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),

        }
    }
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }
    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'
        html = html + '      </ul>     </div>'
        return html;
    }

    
    function open_image(image) {
        if (image == "null") {
            alert("没有可查看的图片。");
        } else {
            var result = image.replace('h_300', 'h_700');
            open(result);
        }
    }
    //编辑
    function Edit(banner_id) {
        open_url("Edit_frm?banner_id=" + banner_id);
    }
    //删除
    function Delete(banner_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("Delete", "get", { banner_id: banner_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "Index?time" + new Date().toString();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }

    $('#post-002 img').each(function () {
        if ($(this).parent().hasClass('fancybox')) return;
        if ($(this).hasClass('nofancybox')) return;
        var alt = this.alt;
        $(this).wrap('<a href="javascript:void(0);" data-src="' + ($(this).attr('data-src') == null ? this.src : $(this).attr('data-src')) + '" title="' + alt + '" class="fancybox"></a>');
    });
    $(".fancybox").click(function () {
        var url = $(this).attr('data-src');
        $("#showImg_img").attr("src", url);
        $("#showImg").show();

    });
    $("#showImg_img").click(function () {
        $("#showImg_img").attr("src", "");
        $("#showImg").hide();
    });
</script>
