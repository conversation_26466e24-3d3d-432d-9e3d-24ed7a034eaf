@{
    ViewBag.Title = "联系我们";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.syst_contact
@using (Ajax.BeginForm("SaveContact", "SystContact", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <blockquote class="layui-elem-quote">
        联系我们
    </blockquote>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">固定电话：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="固定电话 是必需的。" name="tel" value="@Model.tel" placeholder="请输入固定电话">
            <span class="field-validation-valid" data-valmsg-for="tel" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">手机号：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="手机号 是必需的。" name="mobile" value="@Model.mobile" placeholder="请输入 手机号">
            <span class="field-validation-valid" data-valmsg-for="mobile" data-valmsg-replace="true"></span>
        </div>
    </div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">QQ：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="QQ号 是必需的。" name="qq" value="@Model.qq" placeholder="请输入 QQ号">
            <span class="field-validation-valid" data-valmsg-for="qq" data-valmsg-replace="true"></span>
        </div>
    </div>

 
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">邮箱：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="邮箱 是必需的。" name="email" value="@Model.email" placeholder="请输入 邮箱">
            <span class="field-validation-valid" data-valmsg-for="email" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">二维码：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <table>
                    <tr>
                        <td style="padding-right:20px;">
                            <div style="margin-top:10px;text-align:center;margin-bottom:5px;font-size:14px;">客服二维码</div>
                            <div onclick="choosefile('server_qrcode')" style="width:134px ;height:135px;  background-image:url('../Content/images/upLoadbg.png');background-repeat:no-repeat; border:1px solid #c3c2c2;border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%;" id="server_qrcode_upload" src="@Model.server_qrcode" />
                                <input hidden="hidden" id="server_qrcode" name="server_qrcode" value="@Model.server_qrcode" />
                            </div>
                            <div class="layui-inline" style="margin-top:10px;font-size:12px;">
                                140px*138px像素的jpg
                            </div>
                        </td>

                        <td style="padding-right:20px;">
                            <div style="margin-top:10px;text-align:center;margin-bottom:5px;font-size:14px;">公众号二维码</div>
                            <div onclick="choosefile('mp_qrcode')" style="width:134px ;height:135px;  background-image:url('../Content/images/upLoadbg.png');background-repeat:no-repeat; border:1px solid #c3c2c2;border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%;" id="mp_qrcode_upload" src="@Model.mp_qrcode" />
                                <input hidden="hidden" id="mp_qrcode" name="mp_qrcode" value="@Model.mp_qrcode" />
                            </div>
                            <div class="layui-inline" style="margin-top:10px;font-size:12px;">
                                140px*138px像素的jpg
                            </div>
                        </td>

                        <td>
                            <div style="margin-top:10px;text-align:center;margin-bottom:5px;font-size:14px;">小程序二维码</div>
                            <div onclick="choosefile('ap_qrcode')" style="width:134px ;height:135px;  background-image:url('../Content/images/upLoadbg.png');background-repeat:no-repeat; border:1px solid #c3c2c2;border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%;" id="ap_qrcode_upload" src="@Model.ap_qrcode" />
                                <input hidden="hidden" id="ap_qrcode" name="ap_qrcode" value="@Model.ap_qrcode" />
                            </div>
                            <div class="layui-inline" style="margin-top:10px;font-size:12px;">
                                140px*138px像素的jpg
                            </div>
                        </td>

                    </tr>

                </table>
            </div>

        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">公司地址：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="公司地址 是必需的。" name="address" value="@Model.address" placeholder="请输入 公司地址">
            <span class="field-validation-valid" data-valmsg-for="address" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">地图地址：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <table>
                    <tr>
                        <td>
                            <div onclick="choosefile('map_address')" style="width:900px;height:190px;  background-image:url('../Content/images/upLoadbg.png');background-repeat:no-repeat;border:1px solid #c3c2c2; border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%" id="map_address_upload" src="@Model.map_address" />
                                <input hidden="hidden" id="map_address" name="map_address" value="@Model.map_address" />
                            </div>
                            <div class="layui-inline" style="margin-top:10px;font-size:12px;">
                                1280px*300px像素的jpg
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <input type="number" value="@Model.contact_id" name="contact_id" id="contact_id" hidden="hidden" />
}

<div class="site-demo-upbar" hidden="hidden">
    <input type="file" name="file" class="layui-upload-file" id="file">
</div>

<script>
    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
         , method: 'post' //上传接口的http类型
              , before: function (input) {
                  show_loading();
              }
         , success: function (res) {
             close_loading();
             if (res.Isok) {
                 $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                 $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
             }
             else {
                 alert(res.Msg);
             }
         }
        });
    });

    var image_id;
    function choosefile(_image_id) {
        image_id = _image_id;
        var f = document.getElementById("file");
        f.click();
    }
</script>
<script>
    function Save() {

        //开始提交
        
        if (!isNullOrUndefined($("#server_qrcode").val()) && !isNullOrUndefined($("#mp_qrcode").val()) && !isNullOrUndefined($("#ap_qrcode").val()) && !isNullOrUndefined($("#map_address").val())) {
            $("#form").submit()
        }
        else {
            show_msg("图片不能为空。")
        }
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("保存成功.")
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>
