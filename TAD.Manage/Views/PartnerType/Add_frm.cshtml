@{
    ViewBag.Title = "添加联盟合伙人";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@using (Ajax.BeginForm("Add", "PartnerType", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{



    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="classify" id="classify">
                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.partner_type_classify)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.partner_type_classify), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        <option value="@myCode">@strName</option>
                    }
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="名称 是必需的。" name="name" placeholder="请输入 名称">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">级别：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="级别 是必需的。" name="rank" placeholder="请输入 级别数字">
            <span class="field-validation-valid" data-valmsg-for="rank" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">销售价格：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="销售价格 是必需的。" name="sell_price" placeholder="请输入 销售价格">
            <span class="field-validation-valid" data-valmsg-for="sell_price" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">讲师描述：</label>
        <div class="col-sm-9">
            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.config.js"></script>
            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.all.min.js"></script>

            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/lang/zh-cn/zh-cn.js"></script>
            <script id="editor" type="text/plain" style="width:900px;height:410px;">

            </script>

            <script type="text/javascript">
                function getContent() {
                    return UE.getEditor('editor').getContent();
                }

                //实例化编辑器
                //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
                var ue = UE.getEditor('editor');
                ue.ready(function () {
                    UE.getEditor('editor').setContent('', false);
                });
            </script>
        </div>
    </div>
    <textarea type="text" hidden="hidden" id="introduction" name="introduction"></textarea>


    <div class="form-group">
        <label class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入 备注信息">

        </div>
    </div>
}
<script>
    function Save() {
        var introduction = UE.getEditor('editor').getContent();

        $("#introduction").val(introduction);

        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>
<script>
    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
            , method: 'post' //上传接口的http类型
            , before: function (input) {
                show_loading();
            }
            , success: function (res) {
                close_loading();
                if (res.Isok) {
                    $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                    $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
                }
                else {
                    alert(res.Msg);
                }
            }
        });
    });

    var image_id;
    function choosefile(_image_id) {
        image_id = _image_id;
        var f = document.getElementById("file");
        f.click();
    }
</script>

