@{
    ViewBag.Title = "编辑产业合伙人";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.industry_type
@using (Ajax.BeginForm("Edit", "IndustryType", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
 
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="名称 是必需的。" name="name" placeholder="请输入 名称" value="@Model.name">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">级别：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="级别 是必需的。" name="rank" placeholder="请输入 级别数字" value="@Model.rank">
            <span class="field-validation-valid" data-valmsg-for="rank" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">销售价格：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="销售价格 是必需的。" name="sell_price" placeholder="请输入 销售价格" value="@Model.sell_price">
            <span class="field-validation-valid" data-valmsg-for="sell_price" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">提成比例(%)：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="提成比例 是必需的。" name="commission" placeholder="请输入 提成比例" value="@Model.commission">
            <span class="field-validation-valid" data-valmsg-for="commission" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">公开课门票数：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="公开课门票数 是必需的。" name="publicity_publicity" placeholder="请输入 公开课门票数" value="@Model.publicity_publicity">
            <span class="field-validation-valid" data-valmsg-for="publicity_publicity" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">购物卡金额：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="购物卡金额 是必需的。" name="coupon_money" placeholder="请输入 购物卡金额" value="@Model.coupon_money">
            <span class="field-validation-valid" data-valmsg-for="coupon_money" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">讲师描述：</label>
        <div class="col-sm-9">
            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.config.js"></script>
            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.all.min.js"></script>

            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/lang/zh-cn/zh-cn.js"></script>
            <script id="editor" type="text/plain" style="width:900px;height:410px;">

            </script>

            <script type="text/javascript">
                function getContent() {
                    return UE.getEditor('editor').getContent();
                }

                //实例化编辑器
                //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
                var ue = UE.getEditor('editor');
                ue.ready(function () {
                    UE.getEditor('editor').setContent('@Html.Raw(Model.introduction)', false);
                });
            </script>
        </div>
    </div>
    <textarea type="text" hidden="hidden" id="introduction" name="introduction"></textarea>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state == true)
                {
                    <option value="true" selected="selected">启用</option>
                    <option value="false">禁用</option>
                }
                else
                {
                    <option value="true">启用</option>
                    <option value="false" selected="selected">禁用</option>
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入 备注信息" value="@Model.remark">

        </div>
    </div>
    <input value="@Model.type_id" id="type_id" name="type_id" hidden="hidden" />
}
<script>
    function Save() {
        var introduction = UE.getEditor('editor').getContent();

        $("#introduction").val(introduction);

        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>
<script>
    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
            , method: 'post' //上传接口的http类型
            , before: function (input) {
                show_loading();
            }
            , success: function (res) {
                close_loading();
                if (res.Isok) {
                    $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                    $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
                }
                else {
                    alert(res.Msg);
                }
            }
        });
    });

    var image_id;
    function choosefile(_image_id) {
        image_id = _image_id;
        var f = document.getElementById("file");
        f.click();
    }
</script>

