@{
    ViewBag.Title = "编辑课程讲师";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.syst_lecturer
@using (Ajax.BeginForm("Edit", "SystLecturer", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">姓名：</label>
        <div class="col-sm-9">
            <input readonly="readonly" type="text" class="form-control" data-val="true" name="user_id" value="@ViewBag.nickname">
            <span class="field-validation-valid" data-valmsg-for="user_id" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">讲师姓名：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="讲师姓名 是必需的。" value="@Model.name" name="name" placeholder="请输入 讲师姓名">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">讲师简介：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="讲师简介 是必需的。" value="@Model.introduction" name="introduction" placeholder="请输入讲师简介">
            <span class="field-validation-valid" data-valmsg-for="introduction" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state ==true)
                {
                    <option selected="selected" value="true">启用</option>
                    <option value="false">禁用</option>
                }
                else
                {
                    <option value="true">启用</option>
                    <option  selected="selected" value="false">禁用</option>
                }

            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">备注说明：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val-required="备注说明 是必需的。" value="@Model.remark" name="remark" placeholder="请输入 备注说明">
            <span class="field-validation-valid" data-valmsg-for="remark" data-valmsg-replace="true"></span>
        </div>
    </div>

    <input hidden="hidden" id="lecturer_id" name="lecturer_id" value="@Model.lecturer_id" />
}


<script>
    function Save() {
        //开始提交

        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("添加成功！");
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>