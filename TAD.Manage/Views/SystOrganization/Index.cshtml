@{
    ViewBag.Title = "机构管理";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}


<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    @*<td>
                        <button type="button" class="btn btn-success" onclick="open_url('Add_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span>添加机构</button>
                    </td>*@
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>

        
        </form>
    </div>
    <table style=" min-height:150px;" id="table"
           data-toggle="table"
           data-url="GetOrganizationPage"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="name_mobile" data-sortable="true">姓名</th>
                <th data-field="name" data-sortable="true">机构名称</th>
                <th data-field="brief" data-sortable="true">机构简介</th>
                <th data-field="logo_img" data-sortable="true" data-formatter="customer_head_img">企业Logo</th>
                <th data-field="business_code" data-sortable="true">社会代号</th>
                <th data-field="business_img" data-sortable="true" data-formatter="customer_head_img">营业执照</th>
                <th data-field="business_licence_img" data-sortable="true" data-formatter="customer_head_img">视听证</th>
                <th data-field="contacts_name" data-sortable="true">机构联系人</th>
                <th data-field="contacts_mobile" data-sortable="true">联系电话</th>
                <th data-field="contacts_add" data-sortable="true">联系地址</th>
                <th data-field="contacts_email" data-sortable="true">电子邮箱</th>
                <th data-field="state" data-sortable="true">状态</th>
                <th data-field="sort_code" data-sortable="true">排序</th>
                <th data-field="creation_date" data-formatter="formatting_date" data-sortable="true">创建时间</th>
                <th data-field="remark" data-sortable="true">备注说明</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true" >操作</th>
            </tr>
        </thead>
    </table>
  


</div>
<script type="text/javascript">

    function UserHeadUrlFormatter(value, row, index) {
        return "<div>  <table>   <tr><td style='width:65px; '>  <img style='width:60px; ' src='" + row.image_url + "' /> </td>      <td>    <div>" + row.user_name + "【" + row.mobile + "】</div>       <div> " + row.nickname + "  &nbsp; " + row.real_name + "&nbsp; </div>   </td>        </table></div>"
    }

    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);

    }
    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function customer_head_img(value, row, index) {
        return "<a href='" + value + "'><img style='width:60px; ' src='" + value + "' /></a>"
    }
   
    
    function queryParams(params) {

        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),

        }
    }
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }

    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'
        if (row.state == "可用") {
            html = html + '      <li><a href="#" onclick="UpdateState(' + row.organization_id + ','+0+')">禁用</a></li>'
        } else {
            html = html + '      <li><a href="#" onclick="UpdateState(' + row.organization_id + ',' + 1 + ')">启用</a></li>'
        }
        html = html + '      </ul>     </div>'
        return html;
    }
    //编辑
    function Edit(partner_id) {
        open_url("Edit_frm?partner_id=" + partner_id);
    }
    //删除
    function Delete(partner_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("Delete", "get", { partner_id: partner_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "index?time" + new Date().toString();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }
    //更改状态
    function UpdateState(organization_id, state) {
        ajaxRequest("UpdateState", "post", { organization_id: organization_id, state: state }, function (isok, ret) {
            if (ret.Isok) {
                window.location.reload();
            }
        })
    }
</script>

