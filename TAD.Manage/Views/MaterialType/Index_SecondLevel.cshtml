@{
    ViewBag.Title = "Index_SecondLevel";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
@model List<TAD.Manage.Models.MaterialType>
@{
    TAD.Model.material_type type_p = (TAD.Model.material_type)ViewBag.type_p;
}

<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <button type="button" class="btn btn-success" onclick="open_url('Index')" id="submitBTN"><span class="glyphicon  glyphicon-chevron-left"></span> 返回一级分类</button>
            <button type="button" class="btn btn-success" onclick="Add_second('@type_p.type_id')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加二级分类</button>
        </form>
    </div>
    <div class="panel-body">
        <blockquote class="layui-elem-quote">
            @type_p.name <span> 的二级分类如下：</span>
        </blockquote>
        <div class="layui-form" style="padding:0px; margin:0px;">
            <table class="layui-table" style="padding:0px; margin:0px;">
                <colgroup>
                    <col width="60">
                    <col width="160">
                    <col width="90">
                    <col width="90">
                    <col>
                </colgroup>
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>子类数量</th>
                        <th>排序</th>
                        <th>操作</th>

                    </tr>
                </thead>
                <tbody>

                    @if (Model != null & Model.Count > 0)
                    {
                        int rowIndex = 0;
                        foreach (var item in Model)
                        {
                            rowIndex++;
                            <tr>
                                <td>@rowIndex</td>
                                <td>@item.material_type.name</td>
                                <td>@item.three_count</td>
                                <td>@item.material_type.sort_code</td>
                                <td>
                                    <button class="layui-btn layui-btn-normal" onclick="Edit('@item.material_type.type_id')">编辑</button>
                                    <button class="layui-btn layui-btn-warm" onclick="Delete('@item.material_type.type_id')">删除</button>
                                    <button class="layui-btn layui-btn-danger" onclick="AddThree('@item.material_type.type_id')">三级分类</button>

                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    //添加二级分类
    function Add_second(type_id) {
        open_url('AddSecondLeve_frm?type_id=' + type_id);
    }
    //添加三级分类
    function AddThree(type_id) {
        var three_type_id = getUrlParam("type_id");
        open_url('Index_ThreeLevel?type_id=' + type_id + "&three_type_id=" + three_type_id);
    }
    //编辑
    function Edit(type_id) {
        open_url('EditSecond_frm?type_id=' + type_id);
    }
    //删除
    function Delete(type_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("DeleteSecond", "get", { type_id: type_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "Index_SecondLevel?type_id=@type_p.type_id";
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }
        })
    }
</script>