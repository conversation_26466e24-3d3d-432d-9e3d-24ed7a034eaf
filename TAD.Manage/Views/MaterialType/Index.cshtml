@{
    ViewBag.Title = "军火库分类";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
@model List<TAD.Manage.Models.MaterialType>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <button type="button" class="btn btn-success" onclick="open_url('Add_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加一级分类</button>

        </form>
    </div>
    <div class="panel-body">
        <div class="layui-form" style="padding:0px; margin:0px;">
            <table class="layui-table" style="padding:0px; margin:0px;">
                <colgroup>
                    <col width="60">
                    <col width="160">
                    <col width="90">
                    <col width="90">
                    <col width="90">
                    <col>
                </colgroup>
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>分类名称</th>
                        <th>二级总数</th>
                        <th>排序</th>
                        <th>状态</th>
                        <th>创建日期</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model != null && Model.Count > 0)
                    {
                        int rowIndex = 0;
                        foreach (var item in Model)
                        {

                            rowIndex++;
                    <tr>
                        <td>@rowIndex</td>
                        <td>@item.material_type.name</td>
                        <td>
                            @item.second_count
                        </td>
                        
                        
                        <td>@item.material_type.sort_code</td>
                        <td>
                            @{
                                string state_str = item.material_type.state == true ? "启动" : "禁用";}
                            <span>@state_str</span>
                        </td>
                        <td>@item.material_type.creation_date</td>
                        <td>
                            <button class="layui-btn layui-btn-normal" onclick="Edit('@item.material_type.type_id')">编辑</button>
                            <button class="layui-btn layui-btn-warm" onclick="Delete('@item.material_type.type_id')">删除</button>
                            <button class="layui-btn layui-btn-danger" onclick="AddSecond('@item.material_type.type_id')">二级分类</button>
                        </td>
                    </tr>
                    }
                }
                </tbody>
            </table>
        </div>

    </div>
</div>

<script type="text/javascript">
    //添加二级菜单
    function AddSecond(type_id) {
        open_url('Index_SecondLevel?type_id=' + type_id);
    }
    //编辑
    function Edit(type_id) {
        open_url('Edit_frm?type_id=' + type_id);
    }
    //删除
    function Delete(type_id) {
        show_confirm("是否确定删除该条数据？子类数据会所有删除，请慎重执行！", function (isok) {
            if (isok) {
                ajaxRequest("Delete", "get", { type_id: type_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "index?time" + new Date().toString();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }
</script>