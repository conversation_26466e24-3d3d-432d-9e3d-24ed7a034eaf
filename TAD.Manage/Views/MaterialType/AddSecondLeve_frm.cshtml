@{
    ViewBag.Title = "二级菜单";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";

}
@{
    TAD.Model.material_type type_p = (TAD.Model.material_type)ViewBag.parent_type;
}

<blockquote class="layui-elem-quote">

    <span>添加</span> @type_p.name<span> 的二级分类：</span>
</blockquote>

@using (Ajax.BeginForm("AddSecond", "MaterialType", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">分类名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="分类名称 是必需的。" name="name" placeholder="请输入分类名称">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="排序 是必需的。" value="1" name="sort_code" placeholder="数值越大越靠前">
            <span class="field-validation-valid" data-valmsg-for="sort_code" data-valmsg-replace="true"></span>
        </div>
    </div>

    <input hidden="hidden" id="type_id" name="type_id" value="@type_p.type_id" />
}



<script>

    function Save() {

        $("#form").submit();
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();

        if (data.Isok) {
            window.location = "/MaterialType/Index_SecondLevel?type_id="+ @type_p.type_id;
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
 

</script>