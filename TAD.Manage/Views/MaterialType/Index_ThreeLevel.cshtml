@{
    ViewBag.Title = "三级分类";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
@model List<TAD.Model.material_type>
@{
    TAD.Model.material_type type_p = (TAD.Model.material_type)ViewBag.type_p;
}
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <button type="button" class="btn btn-success" onclick="BackPage()" id="submitBTN"><span class="glyphicon  glyphicon-chevron-left"></span> 返回一级分类</button>
            <button type="button" class="btn btn-success" onclick="Add_Three('@type_p.type_id')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加三级分类</button>
        </form>
    </div>
    <div class="panel-body">
        <blockquote class="layui-elem-quote">

            @type_p.name <span> 的三级分类如下：</span>

        </blockquote>
        <div class="layui-form" style="padding:0px; margin:0px;">
            <table class="layui-table" style="padding:0px; margin:0px;">
                <colgroup>
                    <col width="60">
                    <col width="160">
                    <col width="90">
                    <col width="90">
                    <col>
                </colgroup>
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>名称</th>
                        <th>排序</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>

                    @if (Model != null & Model.Count > 0)
                    {
                        int rowIndex = 0;
                        foreach (var item in Model)
                        {
                            rowIndex++;
                            <tr>
                                <td>@rowIndex</td>
                                <td>@item.name</td>
                                <td>@item.sort_code</td>
                                <td>
                                    <button class="layui-btn layui-btn-normal" onclick="Edit('@item.type_id')">编辑</button>
                                    <button class="layui-btn layui-btn-warm" onclick="Delete('@item.type_id')">删除</button>

                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    //添加三级分类
    function Add_Three(type_id) {
        open_url('AddThreeLeve_frm?type_id=' + type_id + "&three_type_id=" + getUrlParam('three_type_id'));
    }
    //编辑
    function Edit(type_id) {
        open_url('EditThree_frm?type_id=' + type_id);
    }
    //返回上一页
    function BackPage() {
        open_url('Index_SecondLevel?type_id=' + getUrlParam('three_type_id'));
    }
    //删除
    function Delete(type_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("DeleteSecond", "get", { type_id: type_id }, function (isok, ret) {
                    if (ret.Isok) {
                         window.location = "Index_ThreeLevel?type_id=@type_p.type_id" +"&three_type_id="+0;
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }
</script>