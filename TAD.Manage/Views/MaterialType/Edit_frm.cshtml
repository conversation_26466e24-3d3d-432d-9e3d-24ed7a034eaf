@{
    ViewBag.Title = "编辑菜单";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@model TAD.Model.material_type

@using (Ajax.BeginForm("Edit", "MaterialType", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">分类名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="name" data-val="true" data-val-required="分类名称 是必需的。" placeholder="请输入分类名称" value="@Model.name">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="排序 是必需的。" name="sort_code" placeholder="请输入排序" value="@Model.sort_code">
            <span class="field-validation-valid" data-valmsg-for="sort_code" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state == true)
                {
                    <option value="true" selected="selected">启用</option>
                    <option value="false">禁用</option>
                }
                else
                {
                    <option value="true">启用</option>
                    <option value="false" selected="selected">禁用</option>
                }
            </select>
        </div>
    </div>

    <input hidden="hidden" name="type_id" id="type_id" value="@Model.type_id" />
    <input hidden="hidden" name="parent_id" id="parent_id" value="@Model.parent_id" />

}
<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        if (data.Isok) {
            window.location = "/MaterialType/index?wq" + new Date().getSeconds();
        }
        else {
            alert("添加失败+" + data.Msg)
        }
    }
</script>