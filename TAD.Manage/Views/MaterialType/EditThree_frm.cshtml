@{
    ViewBag.Title = "编辑三级菜单";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.material_type
@using (Ajax.BeginForm("EditThree", "MaterialType", new AjaxOptions { HttpMethod = "Post", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">分类名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="分类名称 是必需的。" name="name" placeholder="请输入分类名称" value="@Model.name">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="排序 是必需的。" name="sort_code" placeholder="数值越大越靠前" value="@Model.sort_code">
            <span class="field-validation-valid" data-valmsg-for="sort_code" data-valmsg-replace="true"></span>
        </div>
    </div>

    <input hidden="hidden" id="parent_id" name="parent_id" value="@Model.parent_id" />
    <input hidden="hidden" id="type_id" name="type_id" value="@Model.type_id" />
}


<script>
    function Save() {

            $("#form").submit()

    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        if (data.Isok) {
            window.location = "Index_ThreeLevel?type_id=@Model.parent_id" +"&three_type_id="+0;
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>