@{
    ViewBag.Title = "编辑标签";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.course_online_label
@using (Ajax.BeginForm("Edit", "CourseOnlineLabel", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">标签名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="标签名称 是必需的。" name="name" value="@Model.name" placeholder="请输入 标签名称">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">序号：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="序号 是必需的。" name="sort" value="@Model.sort" placeholder="请输入 序号">
            <span class="field-validation-valid" data-valmsg-for="sort" data-valmsg-replace="true"></span>
        </div>
    </div>
    <input name="label_id" id="label_id" hidden="hidden" value="@Model.label_id"/>
}


<script>
    function Save() {
        //开始提交
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("编辑成功！");
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>
