 
@{
    ViewBag.Title = "添加标签";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@using (Ajax.BeginForm("Add", "CourseOnlineLabel", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">标签名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="标签名称 是必需的。" name="name" placeholder="请输入 标签名称">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>
    
}


<script>
    function Save() {
        //开始提交
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("添加成功！");
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>
