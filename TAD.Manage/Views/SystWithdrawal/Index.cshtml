@{
    ViewBag.Title = "提现记录";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
<link href="~/Content/CSS/jquery-confirm.css" rel="stylesheet" />
<script src="~/Content/Scripts/jquery-confirm.js" charset="gbk"></script>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                            &nbsp;
                            <input id="page" class="form-control" style="width: 60px" type="number" value="1" min="1">
                            <span id="search_button" onclick="select_page()" style="cursor:pointer" class="input-group-addon">跳转</span>
                            <div style="margin-left:14px;">
                                <select class="combobox input-large form-control" onchange="select_type()" id="display_type">
                                    <option value="all">显示全部</option>
                                    <option value="wx_app">显示微信APP</option>
                                    <option value="wx_web">显示微信公众号</option>
                                </select>

                            </div>

                        </div>
                    </td>
                    <td>
                        <div style="margin-left:14px;">
                            <span id="search_button" onclick="audit_batch()" style="cursor:pointer" class="input-group-addon">批量审核</span>
                        </div>
                    </td>
                    <td>
                        <div style="margin-left:14px;">
                            <span id="search_button" onclick="we_transfer_batch()" style="cursor:pointer" class="input-group-addon">微信批量转款</span>
                        </div>
                    </td>
                    <td>
                        <div style="margin-left:14px;">
                            <input type="date" name="date" id="date" />
                        </div>
                    </td>
                    <td>&nbsp;&nbsp;&nbsp;兑换金额：<span id="withdrawal_amount"></span></td>
                    <td>&nbsp;&nbsp;&nbsp;已转金额：<span id="already_amount"></span></td>
                    <td>&nbsp;&nbsp;&nbsp;未转金额：<span id="not_amount"></span></td>


                </tr>
                <div style="float:right;margin-top:10px;"> <button type="button" class="btn btn-success" onclick="ExportExcel()" id="submitBTN"><span class="glyphicon glyphicon-plus"></span>导出Excel</button></div>
            </table>
        </form>
    </div>
    <table style=" min-height:300px;" id="table"
           data-toggle="table"
           data-url="GetPageData"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[15,50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams">
        <thead>
            <tr>
                <th data-field="tt" data-checkbox="true"></th>
                <th data-field="index" data-formatter="indexFormatter">索引</th>
                <th data-field="name_mobile">姓名（手机）</th>

                <th data-field="money" data-formatter="formatting_money">兑换金额</th>
                <th data-field="factorage" data-formatter="formatting_money">手续费</th>
                <th data-field="amount_money" data-formatter="formatting_money" style="text-align:right">到帐金额</th>

                <th data-formatter="InfoFormatter">兑换信息</th>
                <th data-field="payment">兑换方式</th>

                <th data-field="state_str">状态</th>
                <th data-field="creation_date_str" data-formatter="formatting_date">兑换时间</th>
                <th data-field="remark">备注</th>

                <th data-field="user_id" data-formatter="operation_menu" data-sortable="true">操作</th>
            </tr>
        </thead>
    </table>
</div>

<script type="text/javascript">



    function select_type() {

        var display_type = $("#display_type").val();

        var $table = $('#table');
        $table.bootstrapTable('selectPage', 1);

        load_data();
    }
    $(function () {
        var $table = $('#table');
        $table.on('load-success.bs.table', function (e, data) {
            $("#withdrawal_amount").text(data.withdrawal_amount);
            $("#already_amount").text(data.already_amount);
            $("#not_amount").text(data.not_amount);
        })


        var display_type = getUrlParam("display_type");

        if (!isNullOrUndefined(display_type)) {
            $("#display_type").val(display_type);
        }

        var date = getUrlParam("date");
        if (!isNullOrUndefined(date)) {
            $("#date").val(date);
        }

        $('#date').bind('input propertychange', function () {
            var display_type = $("#display_type").val();

            var $table = $('#table');
            $table.bootstrapTable('selectPage', 1);
            var date = $("#date").val();

            load_data();

        });


    })

    function audit_batch() {
        var $table = $('#table');
        var data = $table.bootstrapTable('getSelections');
        var arr = [];


        for (var o in data) {
            arr.push(data[o].withdrawal_id);

        }
        if (arr.length > 0) {

            show_confirm("确定对" + arr.length + "笔兑换进行批量审核?", function (isok) {
                if (isok) {
                    show_loading();
                    ajaxRequest_traditional('audit_batch', "post", { ids: arr }, function (isok, ret) {
                        close_loading();
                        if (isok) {
                            if (ret.Isok) {


                                var date = $("#date").val();

                                load_data();

                            }
                            else {
                                alert(ret.Msg);

                                var date = $("#date").val();

                                load_data();
                            }
                        }

                    })

                }
            })
        }
        else {
            alert('请选择。。');
        }

    }


    function we_transfer_batch() {
        var $table = $('#table');
        var data = $table.bootstrapTable('getSelections');
        var arr = [];

        for (var o in data) {
            arr.push(data[o].withdrawal_id);

        }
        if (arr.length > 0) {
            var display_type = getUrlParam("display_type");
            show_confirm("确定对" + arr.length + "笔兑换进行批量转款?", function (isok) {
                if (isok) {
                    show_loading();
                    ajaxRequest_traditional('batch_we_transfer', "post", { ids: arr }, function (isok, ret) {
                        close_loading();
                        if (isok) {
                            if (ret.Isok) {
                                alert(ret.Msg);
                                load_data();
                            }
                            else {
                                alert(ret.Msg);
                                load_data();
                            }
                        }

                    })

                }
            })
        }
        else {
            alert('请选择。。');
        }

    }


    function select_page() {
        var v = $("#page").val();
        var $table = $('#table');
        $table.bootstrapTable('selectPage', v);
    }

    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function InfoFormatter(value, row, index) {
        if (row.payment == '微信APP' || row.payment == '微信公众号') {
            var html = "";
            html = html + "   <span>微信名称：" + row.wx_nickname + "</span><br />";
            html = html + '           <span>微信头像：</span>  <img style="width:30px; height:30px;  margin:0 auto; border-radius:50%" src="' + row.wx_headimgurl + '" /><br />';
            return html;
        }

    }

    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        var v = formatCurrency(value);
        var html = "<div class=\" pull-right\">" + v + "</div>";
        return html;
    }


    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">         审核 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'

        if (row.state == '等待审核') {
            html = html + "<li onclick=\"check_nopass('" + row.withdrawal_id + "','审核不通过','" + row.name + "')\"> <a href='#'>审核不通过</a></li>"

            html = html + "<li onclick=\"check_update('" + row.withdrawal_id + "','审核通过','' )\"> <a href='#'>审核通过</a></li>"

        }
        if (row.state == "审核通过") {
            html = html + "<li onclick=\"check_update('" + row.withdrawal_id + "','等待审核','' )\"> <a href='#'>等待审核</a></li>"

            if (row.payment == '微信APP' || row.payment == '微信公众号') {
                html = html + "<li onclick=\"we_transfer('" + row.withdrawal_id + "','" + row.name + "','" + row.amount_money + "' )\"> <a href='#'>微信转帐</a></li>"
            }

            html = html + "<li onclick=\"check_update('" + row.withdrawal_id + "','转款成功','' )\"> <a href='#'>转款成功</a></li>"

        }

        html = html + '   <li><a href="#" onclick="SetServiceMoney(' + row.withdrawal_id + ')">设置手续费</a></li>'
        html = html + "<li onclick=\"add_remark('" + row.withdrawal_id + "','" + row.remark + "','" + row.name + "' )\"> <a href='#'>备注</a></li>"

        html = html + '      <li><a target="_blank" href="/SystAccount/BillDetail_frm?user_id=' + row.user_id + '"  >账单明细</a></li>'
        html = html + '   <li><a href="#" onclick="UpdateType(' + row.withdrawal_id + ')">切换提现方式</a></li>'

        html = html + '      </ul>     </div>'
        return html;
    }

    function queryParams(params) {
        var date = getUrlParam("date");

        return {
            //每页多少条数据
            page: (params.offset / params.limit) + 1, //页码
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),
            display_type: getUrlParam("display_type"),
            date: date,
        }
    }
    function search_button() {

        var $table = $('#table');
        $table.bootstrapTable('refresh');

    }

    function check_nopass(withdrawal_id, operate, name) {
        show_prompt("对" + name + "兑换进行审核不通过的理由", '', function (value) {
            check_update(withdrawal_id, operate, value);
        })
    }

    function Transferfailed(withdrawal_id, operate, name) {
        show_confirm("就否对" + name + "转款失败操作？", function (isok) {
            if (isok) {
                check_update(withdrawal_id, operate, '转帐失败');
            }
        })
    }

    function load_data() {
        var date = $("#date").val();
        var display_type = $("#display_type").val();


        window.location.href = "index?display_type=" + display_type + "&date=" + date + "&show_sum=1";;
    }

    function we_transfer(withdrawal_id, name, money) {
        var display_type = getUrlParam("display_type");

        show_confirm("确定对" + name + "进行微信转帐金额：" + money + "元?", function (isok) {
            if (isok) {
                show_loading();
                ajaxRequest_simple('we_transfer', { withdrawal_id: withdrawal_id }, function (isok, ret) {
                    close_loading();
                    if (isok) {
                        if (ret.Isok) {
                            load_data();
                        }
                        else {
                            alert(ret.Msg);
                        }
                    }

                })

            }
        })
    }

    function add_remark(withdrawal_id, oldremark, name) {
        var display_type = getUrlParam("display_type");
        show_prompt("请输入备注信息", oldremark, function (value) {
            ajaxRequest("add_remark", "post", { withdrawal_id: withdrawal_id, remark: value }, function (isok, ret) {
                if (isok) {
                    if (ret.Isok) {
                        load_data();
                    }
                }
            });
        });
    }

    function check_update(withdrawal_id, operate, remark) {
        var display_type = getUrlParam("display_type");
        show_loading();
        ajaxRequest_simple('check', { withdrawal_id: withdrawal_id, operate: operate, remark: remark }, function (isok, ret) {
            close_loading();
            if (isok) {
                if (ret.Isok) {
                    load_data();
                }
                else {
                    alert(ret.Msg);
                }
            }

        })
    }


    //设置手续费
    function SetServiceMoney(withdrawal_id) {

        show_confirm("是否确定设置该用户手续费？", function (isok) {
            if (isok) {
                show_loading();
                ajaxRequest("SetServiceMoney", "get", { withdrawal_id: withdrawal_id }, function (isok, ret) {
                    close_loading();
                    if (ret.Isok) {
                        load_data();
                    }
                    else {
                        show_msg("更改失败。" + ret.Msg)
                    }
                });
            }

        })
    }
    //切换提现方式
    function UpdateType(withdrawal_id) {
        show_confirm("是否确定切换提现方式？", function (isok) {
            if (isok) {
                show_loading();
                ajaxRequest("UpdateWithdrawalType", "get", { withdrawal_id: withdrawal_id }, function (isok, ret) {
                    close_loading();
                    if (ret.Isok) {
                        load_data();
                    }
                    else {
                        show_msg("更改失败。" + ret.Msg)
                    }
                });
            }

        })
    }

    //导出Excel
    function ExportExcel() {
      
        show_loading();
        ajaxRequest("ExportUnderAll", "get", { }, function (isok, ret) {
            close_loading();
            if (ret.Isok) {
                open_url(ret.Obj);
            } else {
                alert("导出失败：" + ret.Msg)
            }
        });

    }
</script>