@{
    ViewBag.Title = "添加文章";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@using (Ajax.BeginForm("AddMaterialArticle", "MaterialArticle", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">文章标题：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="文章标题 是必需的。" name="title" placeholder="请输入文章标题">
            <span class="field-validation-valid" data-valmsg-for="title" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">文章副标题：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="headline" placeholder="请输入 文章副标题或链接地址" />
            <span class="field-validation-valid" data-valmsg-for="headline" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">文章分类：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type_id" id="type_id">
                @foreach (TAD.Model.material_type item in (List<TAD.Model.material_type>)(ViewData["material_type"]))
                {
                    <option value="@item.type_id">@item.name</option>
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">分享次数：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="share_count" placeholder="请输入 文章副标题"  value="0"/>
            <span class="field-validation-valid" data-valmsg-for="share_count" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">在线人数：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="link_count" placeholder="请输入 文章副标题"  value="0"/>
            <span class="field-validation-valid" data-valmsg-for="link_count" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">学习人数：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="browse_count" placeholder="请输入 文章副标题"  value="0"/>
            <span class="field-validation-valid" data-valmsg-for="browse_count" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">内容是否为链接：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="is_link" id="is_link">
                <option selected="selected" value="false">否</option>
                <option value="true">是</option>
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">文章内容：</label>
        <div class="col-sm-9">
            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.config.js"></script>
            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.all.min.js"></script>

            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/lang/zh-cn/zh-cn.js"></script>
            <script id="editor" type="text/plain" style="width:690px;height:310px;">

            </script>

            <script type="text/javascript">
                function getContent() {
                    return UE.getEditor('editor').getContent();
                }
                //实例化编辑器
                //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
                var ue = UE.getEditor('editor');
                ue.ready(function () {
                    UE.getEditor('editor').setContent('', false);
                });
            </script>
        </div>
    </div>
    <textarea type="text" hidden="hidden" id="content" name="content"></textarea>

    <input hidden="hidden" id="image_url" name="image_url" value="" />

    <div class="form-group">
        <label class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="sort" value="1" placeholder="请输入 排序号" />
        </div>
    </div>
}
<div class="form-group form-horizontal">
    <label class="col-sm-2 control-label">封面：</label>
    <div class="col-sm-9">
        <div class="site-demo-upload">
            <table>
                <tr>
                    <td>
                        <div onclick="choosefile()" style="width:210px ;height:150px;  background-image:url('/Content/images/upLoadbg.png');background-repeat:no-repeat; border:1px solid #c3c2c2;border-radius:5px; background-position:center center; background-size:20%;">
                            <img style="width:100%;" id="LAY_demo_upload" />
                        </div>

                    </td>
                    <td>说明：700px*510px像素的图片</td>
                </tr>
            </table>
            <div class="site-demo-upbar" hidden="hidden">
                <input type="file" name="file" class="layui-upload-file" id="file">
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    function Save() {
        //编辑器
        var link = $("#is_link").val();
        if (link == "true") {
            var content = UE.getEditor('editor').getContentTxt();
            $("#content").val(content);
        } else {
            var content = UE.getEditor('editor').getContent();
            $("#content").val(content);
        }

        if (!isNullOrUndefined($("#image_url").val())) {
            $("#form").submit()
        } else {
            show_msg("图片不能为空。")
        }
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

    layui.use('upload', function () {
        layui.upload({
            url: '/UploadFiles/Upload_MallProductTypeImg'
            , method: 'post' //上传接口的http类型
            , before: function (input) {
                show_loading();
            }
            , success: function (res) {
                close_loading();
                LAY_demo_upload.src = "http://" + res.Obj;
                $("#image_url").val(LAY_demo_upload.src);
            }
        });
    });
    function choosefile() {
        var f = document.getElementById("file");
        f.click();
    }

</script>