@{
    ViewBag.Title = "编辑素材";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.syst_ad
@using (Ajax.BeginForm("Edit", "SystAd", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">标题：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="标题 是必需的。" name="title" value="@Model.title" placeholder="请输入 标题">
            <span class="field-validation-valid" data-valmsg-for="title" data-valmsg-replace="true"></span>
        </div>
    </div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">封面图片：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <table>
                    <tr>
                        <td>
                            <div onclick="choosefile('cover_url')" style="width:341px ;height:180px;  background-image:url('/Content/images/upLoadbg.png');background-repeat:no-repeat; border:1px solid #c3c2c2;border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%;" id="cover_url_upload" src="@Model.cover_url" />
                                <input hidden="hidden" id="cover_url" name="cover_url" value="@Model.cover_url" />
                            </div>
                        </td>
                    </tr>

                </table>
            </div>
            <div class="layui-inline">
                说明：720px*380px像素的图片
            </div>
        </div>
    </div>
    <div style="height:30px"></div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">简介：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="简介 是必需的。" value="@Model.brief" name="brief" placeholder="请输入 简介">
            <span class="field-validation-valid" data-valmsg-for="brief" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">详情：</label>
        <div class="col-sm-9">
            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.config.js"></script>
            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.all.min.js"></script>

            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/lang/zh-cn/zh-cn.js"></script>
            <script id="editor" type="text/plain" style="width:900px;height:410px;">

            </script>

            <script type="text/javascript">
                function getContent() {
                    return UE.getEditor('editor').getContent();
                }

                //实例化编辑器
                //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
                var ue = UE.getEditor('editor');
                ue.ready(function () {
                    UE.getEditor('editor').setContent('@Html.Raw(Model.details)', false);
                });
            </script>
        </div>
    </div>
    <textarea type="text" hidden="hidden" id="details" name="details"></textarea>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">APP路径：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="标题 APP路径。" name="tag_app_url" value="@Model.tag_app_url" placeholder="请输入 APP路径">
            <span class="field-validation-valid" data-valmsg-for="tag_app_url" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">微信路径：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="标题 微信路径。" name="tag_mp_url" value="@Model.tag_mp_url" placeholder="请输入 微信路径">
            <span class="field-validation-valid" data-valmsg-for="tag_mp_url" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="标题 是必需的。" value="@Model.sort" name="sort" placeholder="请输入 排序">
            <span class="field-validation-valid" data-valmsg-for="sort" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state == true)
                {
                    <option value="true" selected="selected">启用</option>
                    <option value="false">禁用</option>
                }
                else
                {
                    <option value="true">启用</option>
                    <option value="false" selected="selected">禁用</option>
                }
            </select>
        </div>
    </div>

    <input type="number" value="@Model.ad_id" id="ad_id" name="ad_id" hidden="hidden" />
}

<div class="site-demo-upbar" hidden="hidden">
    <input type="file" name="file" class="layui-upload-file" id="file">
</div>

<script>
    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
            , method: 'post' //上传接口的http类型
            , before: function (input) {
                show_loading();
            }
            , success: function (res) {
                close_loading();
                if (res.Isok) {
                    $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                    $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
                }
                else {
                    alert(res.Msg);
                }
            }
        });
    });

    var image_id;
    function choosefile(_image_id) {
        image_id = _image_id;
        var f = document.getElementById("file");
        f.click();
    }
</script>
<script>
    function Save() {
        var details = UE.getEditor('editor').getContent();

        $("#details").val(details);

        //开始提交
        if (!isNullOrUndefined($("#cover_url").val())) {
            $("#form").submit()
        }
        else {
            show_msg("图片不能为空。")
        }
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("编辑成功！");
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>

