@{
    ViewBag.Title = "编辑用户设置";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}


@model TAD.Model.syst_user_config

@using (Ajax.BeginForm("Edit", "SystUserConfig", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">姓名：</label>
        <div class="col-sm-9">
            <input readonly="readonly" type="text" class="form-control" data-val="true"  name="user_id" value="@ViewBag.nickname">
            <span class="field-validation-valid" data-valmsg-for="user_id" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">选择类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type" id="type">

                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.syst_user_config_type)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.syst_user_config_type), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值

                        if (Model.type == myCode)
                        {
                            <option selected="selected" value="@myCode">@strName</option>
                        }
                        else
                        {
                            <option value="@myCode">@strName</option>
                        }
                    }
                }

            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">设置比例(%)：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="比例 是必需的。" name="value" value="@Model.value" placeholder="请输入 设置的比例">
            <span class="field-validation-valid" data-valmsg-for="value" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state == true)
                {
                    <option selected="selected" value="true">启用</option>
                    <option value="false">禁用</option>
                }
                else
                {
                    <option selected="selected" value="true">启用</option>
                    <option value="false">禁用</option>
                }

            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入 备注信息" value="@Model.remark">

        </div>
    </div>
    <input hidden="hidden" value="@Model.config_id" name="config_id"/>
}

<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>