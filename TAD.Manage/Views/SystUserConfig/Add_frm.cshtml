@{
    ViewBag.Title = "添加用户设置";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

<script>
    $(function () {
        $('#mobile_name').bind('input propertychange', function () {
            var mobile_name = $.trim($("#mobile_name").val());
            if (verify_mobile(mobile_name)) {

                ajaxRequest("LoadUser", "get", { mobile_name: mobile_name }, function (isok, ret) {
                    if (ret.Isok) {
                        $("#to_customer").css('display', 'block');
                        $("#to_customer_head_img").attr("src", ret.Obj.head_url);
                        $("#to_customer_name").text(ret.Obj.name);
                        $("#to_customer_mobile").text(ret.Obj.mobile);
                        $("#customer_id").val(ret.Obj.user_id);
                    }
                    else {
                        $("#to_customer").css('display', 'none');
                        $("#customer_id").val('');
                    }
                });
            }
            else {
                $("#to_customer").css('display', 'none');
                $("#customer_id").val('');
            }
        });
    })
</script>

@using (Ajax.BeginForm("Add", "SystUserConfig", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <div class="col-sm-9">
            @Html.Action("SearchUser", "SharedLump")
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">选择类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type" id="type">

                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.syst_user_config_type)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.syst_user_config_type), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        <option value="@myCode">@strName</option>
                    }
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">设置比例(%)：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="比例 是必需的。" name="value" placeholder="请输入 设置的比例">
            <span class="field-validation-valid" data-valmsg-for="value" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入 备注信息">

        </div>
    </div>

}

<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>


