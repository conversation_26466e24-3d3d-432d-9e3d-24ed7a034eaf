@{
    ViewBag.Title = "添加油卡商品";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@using (Ajax.BeginForm("Add", "TopupGascard", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label class="col-sm-2 control-label">商品名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="商品名称 是必需的。" name="name" placeholder="请输入充油卡价格。如：直充100元">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">销售价格：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="销售价格 是必需的。" name="sell_price" placeholder="请输入销售价格。如：100">
            <span class="field-validation-valid" data-valmsg-for="sell_price" data-valmsg-replace="true"></span>
        </div>
    </div>




    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">进货价格：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="进货价格 是必需的。" name="stock_price" placeholder="请输入进货价格。如：100">
            <span class="field-validation-valid" data-valmsg-for="stock_price" data-valmsg-replace="true"></span>
        </div>
    </div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">库存：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="库存 是必需的。" name="inventory" placeholder="请输入库存。如99件">
            <span class="field-validation-valid" data-valmsg-for="inventory" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">备注说明：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark">
            <span class="field-validation-valid" data-valmsg-for="remark" data-valmsg-replace="true"></span>
        </div>
    </div>
}
<script>

    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }


</script>

