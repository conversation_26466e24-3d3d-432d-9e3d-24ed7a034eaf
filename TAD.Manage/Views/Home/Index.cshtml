@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>鬼谷子商学院-后台管理</title>
    <meta name="keywords" content="鬼谷子商学院">
    <meta name="description" content="鬼谷子商学院">
    <!--[if lt IE 9]>
    <meta http-equiv="refresh" content="0;ie.html" />
    <![endif]-->
    <link href="~/Content/plugins/Hplus/css/bootstrap.min.css?v=3.3.6" rel="stylesheet">
    <link href="~/Content/plugins/Hplus/css/font-awesome.min.css?v=4.4.0" rel="stylesheet">
    <link href="~/Content/plugins/Hplus/css/animate.css" rel="stylesheet">
    <link href="~/Content/plugins/Hplus/css/style.css?v=4.1.0" rel="stylesheet">
</head>
@model TAD.Manage.Models.Home
<body class="fixed-sidebar full-height-layout gray-bg" style="overflow:hidden">
    <div id="wrapper">
        <!--左侧导航开始-->
        <nav class="navbar-default navbar-static-side" role="navigation">
            <div class="sidebar-collapse">
                <ul class="nav" id="side-menu">
                    <li class="nav-header" style="  padding-top:10px; padding-bottom:10px;">
                        <div class="dropdown profile-element">

                            <span>

                                @if (string.IsNullOrEmpty(Model.customer.head_img))
                                {

                                    <img style="width:60px;" alt="image" class="img-circle" src="~/Content/images/head_bg.jpg" />
                                }
                                else
                                {
                                    <img style="width:60px;" alt="image" class="img-circle" src="@Model.customer.head_img" />
                                }



                            </span>
                            <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                                <span class="clear">
                                    <span class="block m-t-xs"><strong class="font-bold">@Model.customer.nickname</strong></span>
                                    <span>权限: @ViewBag.role_type</span>
                             
                                </span>
                            </a>
                        </div>
                        <div class="logo-element">
                            YZF
                        </div>
                    </li>
                    @{
                        int index = 0;
                    }
                    @foreach (var item in Model.menus)
                    {

                        if (item.level_code == 1)
                        {
                            if (index == 0)
                            {
                                <li class=" active">

                                    <a href="#">
                                        <i class="fa fa-home"></i>
                                        <span class="nav-label">@item.name</span>
                                        <span class="fa arrow"></span>
                                    </a>
                                    @{
                                        List<TAD.Model.manage_menu> sub_menu = Model.menus.Where(o => o.level_code == 2 && o.parent_id == item.menu_id).ToList();
                                    }
                                    @if (sub_menu.Count > 0)
                                    {
                                        <ul class="nav nav-second-level">
                                            @foreach (var sub_item in sub_menu)
                                            {
                                                <li>
                                                    <a class="J_menuItem" href="@sub_item.url">@sub_item.name</a>
                                                </li>
                                            }

                                        </ul>
                                    }

                                </li>
                            }
                            else
                            {
                                <li>

                                    <a href="#">
                                        <i class="fa fa-home"></i>
                                        <span class="nav-label">@item.name</span>
                                        <span class="fa arrow"></span>
                                    </a>
                                    @{
                                        List<TAD.Model.manage_menu> sub_menu = Model.menus.Where(o => o.level_code == 2 && o.parent_id == item.menu_id).ToList();
                                    }
                                    @if (sub_menu.Count > 0)
                                    {
                                        <ul class="nav nav-second-level">

                                            @foreach (var sub_item in sub_menu)
                                            {
                                                <li>
                                                    <a class="J_menuItem" href="@sub_item.url">@sub_item.name</a>
                                                </li>
                                            }

                                        </ul>
                                    }

                                </li>
                            }

                        }
                        index = index + 1;
                    }
                </ul>
            </div>
        </nav>
        <!--左侧导航结束-->
        <!--右侧部分开始-->
        <div id="page-wrapper" class="gray-bg dashbard-1">
            <div class="row border-bottom">

            </div>
            <div class="row content-tabs">
                <button class="navbar-minimalize  roll-nav roll-left J_tabLeft">
                    <i class="fa fa-backward"></i>
                </button>
                <nav class="page-tabs J_menuTabs">
                    <div class="page-tabs-content">
                        <a href="javascript:;" class="active J_menuTab" data-id="../home/<USER>">首页</a>
                    </div>
                </nav>

                <div class="btn-group roll-nav roll-right">
                    <button class="dropdown J_tabClose" data-toggle="dropdown">
                        关闭操作<span class="caret"></span>
                    </button>
                    <ul role="menu" class="dropdown-menu dropdown-menu-right">
                        <li class="J_tabShowActive">
                            <a>定位当前选项卡</a>
                        </li>
                        <li class="divider"></li>
                        <li class="J_tabCloseAll">
                            <a>关闭全部选项卡</a>
                        </li>
                        <li class="J_tabCloseOther">
                            <a>关闭其他选项卡</a>
                        </li>
                    </ul>
                </div>
                <a href="/login/Index_scan" class="roll-nav roll-right J_tabExit"><i class="fa fa fa-sign-out"></i> 退出</a>
            </div>
            <div class="row J_mainContent" id="content-main">
                <iframe class="J_iframe" name="iframe0" width="100%" height="100%" src="../home/<USER>" frameborder="0" data-id="../home/<USER>" seamless></iframe>
            </div>

        </div>

    </div>
    <!--右侧部分结束-->
    </div>
    <!-- 全局js -->
    <script src="~/Content/plugins/Hplus/js/jquery.min.js?v=2.1.4"></script>
    <script src="~/Content/plugins/Hplus/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="~/Content/plugins/Hplus/js/plugins/metisMenu/jquery.metisMenu.js"></script>
    <script src="~/Content/plugins/Hplus/js/plugins/slimscroll/jquery.slimscroll.min.js"></script>
    <script src="~/Content/plugins/Hplus/js/plugins/layer/layer.min.js"></script>
    <!-- 自定义js -->
    <script src="~/Content/plugins/Hplus/js/hplus.js?v=4.1.0"></script>
    <script type="text/javascript" src="~/Content/plugins/Hplus/js/contabs.js"></script>

</body>
</html>r
