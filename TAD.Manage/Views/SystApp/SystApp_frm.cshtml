@{
    ViewBag.Title = "版本设置";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.syst_app

@using (Ajax.BeginForm("SystApp", "SystContact", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">版本号：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="版本号 是必需的。" name="number" value="@Model.number" placeholder="请输入版本号">
            <span class="field-validation-valid" data-valmsg-for="number" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">版本路径：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="版本路径 是必需的。" name="file_url" value="@Model.file_url" placeholder="请输入 版本路径">
            <span class="field-validation-valid" data-valmsg-for="file_url" data-valmsg-replace="true"></span>
        </div>
    </div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">版本大小：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="版本大小 是必需的。" name="file_size" value="@Model.file_size" placeholder="请输入 版本大小">
            <span class="field-validation-valid" data-valmsg-for="file_size" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">更新要点：</label>
        <div class="col-sm-9">
            <textarea class="form-control" data-val="true" data-val-required="更新要点 是必需的。" name="update_point" placeholder="请输入 更新要点">@Model.update_point</textarea>
            <span class="field-validation-valid" data-valmsg-for="update_point" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">是否IOS：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="is_ios" id="is_ios">
                @if (Model.is_ios == true)
                {
                    <option value="true" selected="selected">是</option>
                    <option value="false">否</option>
                }
                else
                {
                    <option value="true">是</option>
                    <option value="false" selected="selected">否</option>
                }

            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">备注信息：</label>
        <div class="col-sm-9">
            <textarea class="form-control" name="remark" placeholder="请输入 备注信息">@Model.remark</textarea>
        </div>
    </div>

    <input type="number" value="@Model.app_id" name="app_id" id="app_id" hidden="hidden" />
}
<script type="text/javascript">
    function Save() {
        //开始提交
            $("#form").submit()
       
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("保存成功.")
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>
