@{
    ViewBag.Title = "账户明细";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                    <td></td>
                </tr>
            </table>
        </form>
    </div>

    <table style=" min-height:300px;" id="table"
           data-toggle="table"
           data-url="GetPageData"
           data-page-size="10"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="head_img_url" data-formatter="UserHeadUrlFormatter">用户信息</th>
                <th data-field="Balance" data-formatter="formatting_money">余额</th>
                <th data-field="Income" data-formatter="formatting_money">收入</th>
                <th data-field="Expend" data-formatter="formatting_money">支出</th>
                <th data-field="BalanceIntegral" data-formatter="formatting_money">教育积分</th>

                <th data-field="Withdraw" data-formatter="formatting_money">可提金额</th>
                <th data-field="Commission" data-formatter="formatting_money">佣金</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
            </tr>
        </thead>
    </table>

</div>
<script type="text/javascript">

    function formatting_money(value, row, index) {
        return formatCurrency(value);
    }

    function UserHeadUrlFormatter(value, row, index) {
        return "<div>  <table>   <tr><td style='width:65px; '>   <img style='width:60px; ' src='" + row.head_img_url + "' /> </td>      <td>    <div>" + row.user_name + "【" + row.mobile + "】</div>       <div> " + row.nickname + "  &nbsp; " + row.real_name + "&nbsp; " + row.sex + " </div>        </td>    </tr>    </table></div>"
    }


    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'
        html = html + '   <li><a href="#" onclick="OpenPayAmount(' + row.user_id + ')">向账户付款</a></li>'
        html = html + '   <li><a href="#" onclick="OpenGetAmount(' + row.user_id + ')">向账户收款</a></li>'
        html = html + '   <li><a href="#" onclick="OpenBillDetail(' + row.user_id + ')">账单明细</a></li>'
        html = html + '      </ul>     </div>'
        return html;
    }

    function queryParams(params) {
        var key = $.trim($('#search_text').val())
        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: key,

        }
    }
    function search_button() {



        var $table = $('#table');
        $table.bootstrapTable('refresh');

    }

    //向客户付款
    function OpenPayAmount(user_id) {
     
        open_url("UserPaymentForm?user_id=" + user_id);
    }
    //向客户收款
    function OpenGetAmount(user_id) {
            open_url("UserGatheringForm?user_id=" + user_id);
    }
    //打开账单明细
    function OpenBillDetail(user_id) {
        open_url("BillDetail_frm?user_id=" + user_id);
    }
</script>

