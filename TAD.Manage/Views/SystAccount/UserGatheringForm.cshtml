@{
    ViewBag.Title = "向账号收款";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Manage.Models.SystAccount

@using (Ajax.BeginForm("GatheringAmount", "SystAccount", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">姓名：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="name" readonly="readonly" value="@Model.user.user_name (@Model.user.mobile)">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">当前余额：</label>
        <div class="col-sm-9">
            <input readonly="readonly" type="number" class="form-control" data-val="true" name="blance" value="@Model.blance">
            <span class="field-validation-valid" data-valmsg-for="blance" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">选择类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="coin_type" id="coin_type">

                @foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.syst_account_coin_type)))
                {
                    string strName = Enum.GetName(typeof(TAD.Model.Enum.syst_account_coin_type), myCode);//获取名称
                    string strVaule = myCode.ToString();//获取值
                    <option value="@strVaule">@strName</option>
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">收款类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type" id="type">

                @foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.base_account_type)))
                {
                    string strName = Enum.GetName(typeof(TAD.Model.Enum.base_account_type), myCode);//获取名称
                    string strVaule = myCode.ToString();//获取值
                    <option value="@strVaule">@strName</option>
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="text" class="col-sm-2 control-label">收款金额：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="收款金额 是必需的。" name="amount" placeholder="请输入 收款金额">
            <span class="field-validation-valid" data-valmsg-for="amount" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">备注说明：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="description" id="description" placeholder="请输入 备注说明">
        </div>
    </div>
    <input hidden="hidden" id="user_id" name="user_id" value="@Model.user.user_id" />
}
<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "/SystAccount/Index?time=" + new Date().toString();
        }
        else {
            alert("添加失败" + data.Msg)
        }
    }
</script>
