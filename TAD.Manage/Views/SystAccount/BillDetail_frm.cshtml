@{
    ViewBag.Title = "账单明细";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="self.location = document.referrer;" id="submitBTN"><span class="glyphicon  glyphicon-chevron-left"></span> 返回</button>

                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <table style=" min-height:300px;" id="table"
           data-toggle="table"
           data-url="GetBillDetailPage"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="name_mobile">姓名（手机）</th>
                <th data-field="amount" data-formatter="formatting_money">消费金额</th>
                <th data-field="balance" data-formatter="formatting_money">余额</th>
                <th data-field="payment_str">支付方式</th>
                <th data-field="coin_type_str">账户类型</th>
                <th data-field="type_str">账单类型</th>
                <th data-field="state_str">账单状态</th>
                <th data-field="creation_date_str" data-formatter="formatting_date">账单日期</th>
                <th data-field="remark">备注说明</th>
            </tr>
        </thead>
    </table>
</div>
<script type="text/javascript">
    function indexFormatter(value, row, index) {
        return index + 1;
    }


    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);
    }

    function queryParams(params) {

        return {
            //每页多少条数据
            page: (params.offset / params.limit) + 1, //页码
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),
            user_id: getUrlParam("user_id"),
        }
    }
    function search_button() {

        var $table = $('#table');
        $table.bootstrapTable('refresh');

    }


</script>