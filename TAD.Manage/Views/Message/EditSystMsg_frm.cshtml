@{
    /**/

    ViewBag.Title = "编辑系统消息";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.syst_message
@using (Ajax.BeginForm("EditSystMsg", "Message", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    if (Model != null)
    {


        <div class="form-group">
            <label class="col-sm-2 control-label">标题：</label>
            <div class="col-sm-9">
                <input type="text" class="form-control" data-val="true" data-val-required="标题 是必需的。" name="title" placeholder="请输入 标题" value="@Model.title">
                <span class="field-validation-valid" data-valmsg-for="title" data-valmsg-replace="true"></span>
            </div>
        </div>

        <div class="form-group">
            <label for="inputPassword" class="col-sm-2 control-label">封面图片：</label>
            <div class="col-sm-9">
                <div class="layui-inline">
                    <table>
                        <tr>
                            <td>
                                <div onclick="choosefile('image_url')" style="width:341px ;height:180px;  background-image:url('/Content/images/upLoadbg.png');background-repeat:no-repeat; border:1px solid #c3c2c2;border-radius:5px; background-position:center center; background-size:50%;">
                                    <img style="width:100%;" id="image_url_upload" src="@Model.image_url" />
                                    <input hidden="hidden" id="image_url" name="image_url" value="@Model.image_url" />
                                </div>
                            </td>
                        </tr>

                    </table>
                </div>
                <div class="layui-inline">
                    说明：720px*380px像素的图片
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">简介：</label>
            <div class="col-sm-9">
                <textarea type="text" class="form-control" data-val="true" data-val-required="简介 是必需的。" name="explain" placeholder="请输入 简介">@Model.explain</textarea>
                <span class="field-validation-valid" data-valmsg-for="explain" data-valmsg-replace="true"></span>
            </div>
        </div>


        <div class="form-group">
            <label for="inputPassword" class="col-sm-2 control-label">视频介绍：</label>
            <div class="col-sm-9">
                <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.config.js"></script>
                <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.all.min.js"></script>

                <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/lang/zh-cn/zh-cn.js"></script>
                <script id="editor" type="text/plain" style="width:690px;height:310px;">

                </script>

                <script type="text/javascript">
                function getContent() {
                    return UE.getEditor('editor').getContent();
                }

                //实例化编辑器
                //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
                var ue = UE.getEditor('editor');
                ue.ready(function () {
                    UE.getEditor('editor').setContent('@Html.Raw(Model.content)', false);
                });
                </script>
            </div>
        </div>
        <textarea type="text" hidden="hidden" id="content" name="content"></textarea>

        <div class="form-group">
            <label class="col-sm-2 control-label">排序：</label>
            <div class="col-sm-9">
                <input type="number" class="form-control" data-val="true" data-val-required="排序号数 是必需的。" name="sort_code" placeholder="请输入 排序号数" value="@Model.sort_code">
                <span class="field-validation-valid" data-valmsg-for="sort_code" data-valmsg-replace="true"></span>
            </div>
        </div>
        <div class="form-group">
            <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
            <div class="col-sm-9">
                <select class="combobox form-control" name="state" id="state">
                    @if (Model.state == 1)
                    {
                        <option value="1" selected="selected">启用</option>
                        <option value="0">禁用</option>
                    }
                    else
                    {
                        <option value="1">启用</option>
                        <option value="0" selected="selected">禁用</option>
                    }
                </select>
            </div>
        </div>
        <input hidden="hidden" type="text" value="@Model.message_id" id="message_id" name="message_id"/>

    }
    else
    {
        <div style="text-align:center;padding:10px;">暂无任何系统消息</div>
    }
}


<div class="site-demo-upbar" hidden="hidden">
    <input type="file" name="file" class="layui-upload-file" id="file">
</div>

<script>
    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
            , method: 'post' //上传接口的http类型
            , before: function (input) {
                show_loading();
            }
            , success: function (res) {
                close_loading();
                if (res.Isok) {
                    $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                    $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
                }
                else {
                    alert(res.Msg);
                }
            }
        });
    });

    var image_id;
    function choosefile(_image_id) {
        image_id = _image_id;
        var f = document.getElementById("file");
        f.click();
    }
</script>
<script>
    function Save() {
        var introduction = UE.getEditor('editor').getContent();
        $("#content").val(introduction);
        if (!isNullOrUndefined($("#image_url").val())) {
            $("#form").submit()
        }
        else {
            show_msg("图片不能为空。")
        }
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "SystMessage_Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>
