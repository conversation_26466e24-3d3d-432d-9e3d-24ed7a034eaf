@{
    ViewBag.Title = "编辑主页导航图";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.course_banner
@using (Ajax.BeginForm("EditBanner", "CourseBanner", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">产品图片：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <table>
                    <tr>
                        <td>
                            <div onclick="choosefile('image_url')" style="width:200px;height:150px;border:solid 1px #808080;  background-image:url('/Content/images/upLoadbg.png');background-repeat:no-repeat; border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%" id="image_url_upload" src="@Model.image_url" />
                                <input hidden="hidden" id="image_url" name="image_url" value="@Model.image_url" />
                            </div>
                        </td>
                    </tr>

                </table>
            </div>
            <div class="layui-inline" style="padding-bottom:20px;padding-top:20px;">
                说明：1920px*710px像素的图片
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">产品选择：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="product_id" id="product_id">
                <option value="0" selected="selected">请选择</option>
                @foreach (TAD.Model.course_product item in (List<TAD.Model.course_product>)(ViewData["course_product"]))
                {
                    if (Model.product_id == item.product_id)
                    {
                        <option selected="selected" value="@item.product_id">@item.name&nbsp;(@item.name)</option>
                    }
                    else
                    {
                        <option value="@item.product_id">@item.name&nbsp;(@item.name)</option>
                    }

                }
            </select>

        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" value="@Model.sort" name="sort" placeholder="请输入 序号">
            <span class="field-validation-valid" data-valmsg-for="sort" data-valmsg-replace="true"></span>
        </div>
    </div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state == true)
                {
                    <option value="true" selected="selected">启用</option>
                    <option value="false">禁用</option>
                }
                else
                {
                    <option value="true">启用</option>
                    <option value="false" selected="selected">禁用</option>
                }
            </select>
        </div>
    </div>
    <input type="number" value="@Model.banner_id" name="banner_id" id="banner_id" hidden="hidden" />
}

<div class="site-demo-upbar" hidden="hidden">
    <input type="file" name="file" class="layui-upload-file" id="file">
</div>

<script>
    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
         , method: 'post' //上传接口的http类型
              , before: function (input) {
                  show_loading();
              }
         , success: function (res) {
             close_loading();
             if (res.Isok) {
                 $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                 $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
             }
             else {
                 alert(res.Msg);
             }
         }
        });
    });

    var image_id;
    function choosefile(_image_id) {
        image_id = _image_id;
        var f = document.getElementById("file");
        f.click();
    }
</script>
<script>
    function Save() {

        //开始提交
        if (!isNullOrUndefined($("#image_url").val())) {
            $("#form").submit()
        }
        else {
            show_msg("图片不能为空。")
        }
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("添加成功！");
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>
