@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('AddRole_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>


        </form>
    </div>
    <table style=" min-height:150px;" id="table"
           data-toggle="table"
           data-url="GetCourseCommPage"
           data-page-size="10"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="name_mobile" data-sortable="true">姓名</th>
                <th data-field="tag_name" data-sortable="true">课程标题</th>
                <th data-field="content" data-sortable="true">评价内容</th>
                <th data-field="type_str" data-sortable="true">类型</th>
                <th data-field="state_str" data-sortable="true">状态</th>
                <th data-field="creation_date_str" data-formatter="formatting_date" data-sortable="true">创建时间</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
            </tr>
        </thead>
    </table>



</div>
<script type="text/javascript">



    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);

    }
    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function customer_head_img(value, row, index) {
        return "<img style='width:60px; ' src='" + value + "' />"
    }

    function queryParams(params) {

        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),
            display_type: getUrlParam("display_type"),
        }
    }
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }

    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'

        html = html + "<li onclick=\"check_nopass('" + row.comment_id + "','审核通过')\"> <a href='#'>审核通过</a></li>"
        html = html + "<li onclick=\"check_nopass('" + row.comment_id + "','审核失败')\"> <a href='#'>审核不通过</a></li>"
        html = html + "<li onclick=\"ReplyContent('" + row.comment_id + "')\"> <a href='#'>回复</a></li>"
        html = html + "<li onclick=\"DeleteReply('" + row.comment_id + "')\"> <a href='#'>删除评论</a></li>"
        html = html + '      </ul>     </div>'
        return html;
    }

    //审核
    function check_nopass(id, type) {

        ajaxRequest("UpdateState", "post", { id: id, type: type }, function (isok, ret) {

            if (ret.Isok) {
                window.location.reload();
            } else {
                alert(ret.Msg);
            }
        });
    }
    //回复内容
 
    function ReplyContent(comment_id) {
        show_loading();
        show_prompt("请输入备注信息", '', function (value) {
            ajaxRequest("ReplyContent", "post", { comment_id: comment_id, content: value }, function (isok, ret) {
                close_loading();
                if (isok) {
                    if (ret.Isok) {
                        load_data();
                    }
                }
            });
        });
    }
    function DeleteReply(comment_id) {

        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("DeleteReply", "get", { comment_id: comment_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "index?time" + new Date().toString();
                    }
                    else {
                        show_msg("失败。" + ret.Msg)
                    }
                });
            }

        })
    }
</script>


