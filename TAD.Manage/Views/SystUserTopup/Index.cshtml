@{
    ViewBag.Title = "充值记录";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>

    <table style=" min-height:150px;" id="table"
           data-toggle="table"
           data-url="GetTopUpPage"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter" data-width="20">ID</th>
                <th data-field="name_mobile" >姓名</th>
                <th data-field="amount" data-formatter="formatting_money">充值金额</th>
                <th data-field="payment_str">支付类型</th>
                <th data-field="state_str">状态</th>
                <th data-field="creation_date_str" data-formatter="formatting_date">充值日期</th>
                <th data-field="remark">备注说明</th>
   
            </tr>
        </thead>
    </table>



</div>
<script type="text/javascript">
    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);

    }
    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function queryParams(params) {

        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),

        }
    }
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }
  
</script>