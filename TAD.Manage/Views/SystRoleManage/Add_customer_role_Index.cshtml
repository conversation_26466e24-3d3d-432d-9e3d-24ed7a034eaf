@{
    ViewBag.Title = "添加用户权限";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@using (Ajax.BeginForm("Add_customer_role", "SystRoleManage", new AjaxOptions { HttpMethod = "Post", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <div class="col-sm-9">
            @Html.Action("SearchUser", "SharedLump")
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">选择角色：</label>
        <div class="col-sm-9">
            @foreach (TAD.Model.syst_role item in (List<TAD.Model.syst_role>)(ViewData["role"]))
            {
                <div class="checkbox">
                    <input id="@item.role_id" name="action" value="@item.role_id" type="checkbox">@item.role_name
                </div>
            }
        </div>
    </div>
    <div class="form-group">

        <label for="inputPassword" class="col-sm-2 control-label">备注说明：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" id="description" placeholder="">
        </div>
    </div>
}
<script>
    function Save() {
        var action_str = "";
        $('input[name="action"]:checked').each(function () {//遍历每一个名字为interest的复选框，其中选中的执行函数
            // action_list.push($(this).val());//将选中的值添加到数组chk_value中
            action_str = action_str + "," + $(this).val()
        });
        if (action_str == "") {
            alert("最少选择一个功能！");
            return;
        }
         
        var description = $("#description").val();
        show_loading();
        ajaxRequest_simple("Add_customer_role", { user_id: $("#user_id").val(), action_str: action_str, description: description }, function (isok, ret) {
            close_loading();
            if (ret.Isok) {
                window.location = "/SystRoleManage/index?wq" + new Date().getSeconds();
            } else {
                alert("添加失败：" + ret.Msg)
            }
        });
    }
</script>
 