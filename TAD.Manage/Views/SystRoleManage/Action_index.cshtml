@{
    ViewBag.Title = "功能管理";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
@model List<TAD.Model.syst_action>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('Add_action_frm')" id="submitBTN"><span class="glyphicon  glyphicon-list-alt"></span> 添加功能</button>
                        <button type="button" class="btn btn-success" onclick="window.location.href = '../SystRoleManage/Index'" id="submitBTN"><span class="glyphicon glyphicon-arrow-left"></span> 返回</button>
                        <div style="float:right;line-height:30px;">系统功能列表</div>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">

                <thead>
                    <tr>
                        <th style="width:50px;">序号</th>
                        <th>功能名称</th>
                        <th>功能按钮</th>
                        <th>备注</th>
                        <th style="width:130px; ">操作</th>
                    </tr>
                </thead>
                <tbody id="search_table">
                    @if (Model != null && Model.Count > 0)
                    {
                        int rowIndex = 0;
                        foreach (var item in Model)
                        {
                            rowIndex++;
                            <tr>
                                <td>@rowIndex</td>
                                <td>@item.action_name</td>
                                <td>@item.button_id</td>
                                <td>@item.remark</td>
                                <td>
                                    <div class="btn-toolbar" role="toolbar">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-default" onclick="Edit_action('@item.action_id')">编辑</button>
                                            <button type="button" class="btn btn-default" onclick="Delete('@item.action_id')">删除</button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    //编辑
    function Edit_action(action_id) {
        window.location.href = "Edit_action_index_load?action_id=" + action_id;
    }

    function Delete(action_id) {
        if (confirm("是否确定删除该条数据？")) {
            ajaxRequest("Delete_action", "get", { action_id: action_id }, function (isok, ret) {
                if (ret.Isok) {
                    window.location = "/SystRoleManage/Action_index?wq" + new Date().getSeconds();
                }
            });
        }
    }
</script>