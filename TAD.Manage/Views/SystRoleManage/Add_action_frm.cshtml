@{
    ViewBag.Title = "添加功能";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@using (Ajax.BeginForm("Add_action", "SystRoleManage", new AjaxOptions { HttpMethod = "Post", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">功能名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="功能名称 是必需的。" name="action_name" placeholder="请输入功能名称">
            <span class="field-validation-valid" data-valmsg-for="action_name" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">功能按钮：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="功能按钮 是必需的。" name="button_id" placeholder="请输入功能按钮">
            <span class="field-validation-valid" data-valmsg-for="button_id" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">备注说明：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入备注说明">
        </div>

    </div>
}
<script>
    function Save()
    {
        $("#form").submit()
    }
    function OnSuccess(data)
    {
        if (data.Isok) {
            window.location = "/SystRoleManage/Action_index?wq" + new Date().getSeconds();
        }
        else {
           alert("添加失败+"+data.Msg)
        }
    }
</script>