@{
    ViewBag.Title = "角色管理";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

@model List<TAD.Manage.Models.Base_role_action>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('Add_role_action_frm')" id="submitBTN"><span class="glyphicon  glyphicon-list-alt"></span> 添加角色</button>

                        <button type="button" class="btn btn-success" onclick="window.location.href = '../SystRoleManage/Index'" id="submitBTN"><span class="glyphicon glyphicon-arrow-left"></span> 返回</button>
                        <div style="float:right;line-height:30px;">角色所具有的功能权限列表</div>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th style="width:50px;">序号</th>
                        <th width="100">角色名称</th>
                        <th>功能名称</th>
                        <th style="width:130px; ">操作</th>
                    </tr>
                </thead>
                <tbody id="search_table">
                    @if (Model != null && Model.Count > 0)
                    {
                        int rowIndex = 0;
                        foreach (var item in Model)
                        {
                            rowIndex++;
                            <tr>
                                <td>@rowIndex</td>
                                <td>@item.role.role_name</td>
                                <td>
                                    @foreach (var action in item.actionList)
                                    {
                                        <span>@action.syst_action.action_name 、</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-toolbar" role="toolbar">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-default" onclick="Edit('@item.role.role_id')">编辑</button>
                                            <button type="button" class="btn btn-default" onclick="Delete('@item.role.role_id')">删除</button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    function Edit(role_id) {

        window.location.href = "Edit_actio_index?role_id=" + role_id;
    }

    //删除
    function Delete(role_id) {
        if (confirm("是否确定删除该条数据？")) {
            ajaxRequest("Delete_role", "get", { role_id: role_id }, function (isok, ret) {
                if (ret.Isok) {
                    window.location = "/BaseRoleManage/role_action_index?wq" + new Date().getSeconds();
                }
            });
        }
    }


</script>