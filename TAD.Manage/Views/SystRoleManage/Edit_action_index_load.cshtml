@{
    ViewBag.Title = "编辑功能";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.syst_action
@using (Ajax.BeginForm("Edit_action", "SystRoleManage", new AjaxOptions { HttpMethod = "Post", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">功能名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="action_name" data-val="true" data-val-required="功能名称 是必需的。" placeholder="请输入功能名称" value="@Model.action_name">
            <span class="field-validation-valid" data-valmsg-for="action_name" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">功能按钮：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="button_id" data-val="true" data-val-required="功能按钮 是必需的。" placeholder="请输入功能按钮" value="@Model.button_id">
            <span class="field-validation-valid" data-valmsg-for="button_id" data-valmsg-replace="true"></span>

        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">备注说明：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入备注说明" value="@Model.remark">
        </div>
    </div>

    //以下为隐藏控件
    <input hidden="hidden" name="action_id" value="@Model.action_id" />
}
<script>
    function Save() {
        $("#form").submit()
    }
    function OnSuccess(data) {
        if (data.Isok) {
            window.location = "/SystRoleManage/Action_index?wq" + new Date().getSeconds();
        }
        else {
            alert("添加失败+" + data.Msg)
        }
    }
</script>