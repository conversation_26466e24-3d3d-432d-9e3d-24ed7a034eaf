@{
    ViewBag.Title = "权限管理";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
@model List<TAD.Manage.Models.SystRoleManage>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('Add_customer_role_Index')" id="submitBTN"><span class="glyphicon  glyphicon-list-alt"></span> 添加用户权限</button>
                        <button type="button" class="btn btn-success" onclick="open_url('Action_index')" id="submitBTN"><span class="glyphicon  glyphicon-list-alt"></span> 功能管理</button>
                        <button type="button" class="btn btn-success" onclick="open_url('Role_action_index')" id="submitBTN"><span class="glyphicon  glyphicon-list-alt"></span> 角色管理</button>
                        <div style="float:right;line-height:30px;">权限管理一览表</div>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">

                <thead>
                    <tr>
                        <th width="50">序号</th>
                        <th width="100">姓名</th>
                        <th width="100">角色</th>
                        <th>权限</th>
                        <th style="width:130px;">备注说明</th>
                        <th style="width:130px;">创建时间</th>

                        <th style="width:130px;">操作</th>
                    </tr>
                </thead>
                <tbody id="search_table">
                    @if (Model != null && Model.Count > 0)
                    {
                        int rowIndex = 0;
                        foreach (var item in Model)
                        {
                            rowIndex++;
                            <tr>
                                <td>@rowIndex</td>
                                <td>@item.syst_user.nickname ( @item.syst_user.real_name ) @item.syst_user.mobile</td>
                                <td>@item.syst_role.remark</td>

                                <td>
                                    @foreach (var action in item.syst_role_action_list)
                                    {
                                        <span>@action.syst_action.action_name 、</span>
                                    }
                                </td>
                                <td>
                                    @item.syst_user_role.description
                                </td>
                                <td>
                                    @item.syst_user_role.creation_date.ToString()
                                </td>
                                <td>
                                    <div class="btn-toolbar" role="toolbar">
                                        <div class="btn-group">


                                            <button type="button" class="btn btn-default" onclick="Edit('@item.syst_user_role.user_role_id')">编辑</button>


                                            <button type="button" class="btn btn-default" onclick="Delete_customer_role('@item.syst_user_role.user_role_id')">删除</button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    }

                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    //编辑
    function Edit(user_role_id) {
        window.location.href = "Edit_customer_role_index?user_role_id=" + user_role_id;
    }
    //删除
    function Delete_customer_role(user_role_id) {

        if (confirm("是否确定删除该条信息？")) {
            ajaxRequest("Delete_customer_role", "get", { user_role_id: user_role_id }, function (isok, ret) {
                if (ret.Isok) {
                    window.location = "/SystRoleManage/index?wq" + new Date().getSeconds();
                }
                else {
                    alert("删除失败。" + ret.Msg)
                }
            });
        }
    }
</script>