@{
    ViewBag.Title = "编辑用户权限";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@model List<TAD.Model.syst_role_action>

@using (Ajax.BeginForm("", "BaseRoleManage", new AjaxOptions { HttpMethod = "Post", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">角色名称：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="角色名称 是必需的。" id="role_name" name="role_name" value="@ViewBag.role_name" placeholder="请输入 角色名称">
            <span class="field-validation-valid" data-valmsg-for="role_name"  data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">功能选择：</label>
        <div class="col-sm-9">


            @foreach (TAD.Model.syst_action item in (List<TAD.Model.syst_action>)(ViewData["action"]))
            {
                <div class="checkbox">
                    @if (Model.Where(o => o.action_id == item.action_id).FirstOrDefault() == null)
                    {
                        <input id="@item.action_id" name="action" value="@item.action_id" type="checkbox">@item.action_name
                    }
                    else
                    {
                        <input id="@item.action_id" name="action" value="@item.action_id" type="checkbox" checked="checked">@item.action_name
                    }
                </div>
            }
        </div>
    </div>
}
<script>
    function Save() {
        var role_name = $("#role_name").val();
        var action_str = "";
        $('input[name="action"]:checked').each(function () {//遍历每一个名字为interest的复选框，其中选中的执行函数
            // action_list.push($(this).val());//将选中的值添加到数组chk_value中
            action_str = action_str + "," + $(this).val()
        });
        if (action_str == "") {
            alert("最少选择一个功能！");
            return;
        } else {
            show_loading();
            ajaxRequest_simple("../SystRoleManage/Edit_actio_frm", { role_id: @ViewBag.role_id, action_str: action_str, role_name: role_name }, function (isok, ret) {
                close_loading();
                if (ret.Isok) {
                    window.location = "/SystRoleManage/Role_action_index?wq" + new Date().getSeconds();
                } else {
                    alert("添加失败：" + ret.Msg)
                }
            });
        }
    }

</script>