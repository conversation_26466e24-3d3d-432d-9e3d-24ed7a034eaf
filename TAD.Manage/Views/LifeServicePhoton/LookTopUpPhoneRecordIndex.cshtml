@{
    ViewBag.Title = "话费记录";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                            <span style="margin-left:20px;font-size:16px;">充值成功总金额：@ViewBag.pay_money_sum 元</span>
                        </div>
                        
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <div class="panel-body" style="padding:0px  ">
        <div class="layui-tab layui-tab-card" lay-filter="order_state" style="padding:0 ;margin:0px;">
            <ul class="layui-tab-title">
                <li id="all">全部订单</li>
                <li id="notpay">未支付</li>
                <li id="alreadypay">已支付</li>
                <li id="topuping">充值中</li>
                <li id="topupsucceed">充值成功</li>
                <li id="topuploser">充值失败</li>
                <li id="refund">已退款</li>
            </ul>
            <table style=" min-height:300px;" id="table"
                   data-toggle="table"
                   data-url="GetTopUpPhoneRecord"
                   data-page-size="50"
                   data-side-pagination="server"
                   data-toolbar="#toolbar"
                   data-pagination="true"
                   data-page-list="[50, 100,150]"
                   data-cookie="true"
                   data-cookie-id-table="saveId"
                   data-query-params="queryParams">
                <thead>
                    <tr>
                        <th data-field="index" data-formatter="indexFormatter" data-width="20">序号</th>
                        <th data-field="product_name">商品名称</th>
                        <th data-field="customer_name">充值姓名</th>
                        <th data-field="order_sn">订单号</th>
                        <th data-field="mobile">充值号码</th>
                        <th data-field="explain">说明</th>
                        <th data-field="pay_money">支付金额</th>
                        <th data-field="payment_str">支付方式</th>
                        <th data-field="pay_date_str" data-formatter="formatting_date">支付日期</th>
                        <th data-field="complete_date_str" data-formatter="formatting_date">到账日期</th>
                        <th data-field="state_str">状态</th>
                        <th data-field="is_commission_str">结算</th>
                        <th data-field="creation_date_str" data-formatter="formatting_date">创建日期</th>
                        <th data-field="remark">备注说明</th>
                        <th data-field="customer_id" data-formatter="operation_menu" data-sortable="true">操作</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>


@*分页js*@
<script type="text/javascript">
    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);
    }

    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function queryParams(params) {
        var type = getUrlParam("type_key");
        if (isNullOrUndefined(type)) {
            type = "all"
        }
        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),
            type_key:type
        }
    }
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作
    layui.use('element', function () {
        var element = layui.element();

    });
    $(function () {

        var type = getUrlParam("type_key");
        if (isNullOrUndefined(type)) {
            type = "all"
        }

        $('.layui-tab-title li').each(function () {
            if ($(this).attr("id") == type) {
                $(this).addClass("layui-this");
            }
        });

        $('.layui-tab-title li').click(function () {
            var $table = $('#table');
            $table.bootstrapTable('selectPage', 1);
            window.location.href = "LookTopUpPhoneRecordIndex?type_key=" + $(this).attr("id") + "&time=" + Date.now().toString();
        });
    })
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }
    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown"> 操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'

        if (isNullOrUndefined(row.remark)) {
            html = html + "<li onclick=\"Addremark('" + row.record_id + "','')\"> <a href='#'>备注</a></li>"

        } else {
            html = html + "<li onclick=\"Addremark('" + row.record_id + "','" + row.remark + "','" + row.name + "' )\"> <a href='#'>备注</a></li>"

        }

        html = html + '      </ul>     </div>'
        return html;
    }

    //添加备注

    function Addremark(record_id, oldremark) {
        show_prompt("请输入备注信息", oldremark, function (value) {
            ajaxRequest("AddRemark", "post", { record_id: record_id, remark: value }, function (isok, ret) {
                if (isok) {
                    if (ret.Isok) {
                        window.location = "LookTopUpPhoneRecordIndex?time=" + new Date().toString();
                    }
                }
            });
        });
    }
</script>
