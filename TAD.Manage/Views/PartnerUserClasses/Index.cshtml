@{
    ViewBag.Title = "联盟合伙人级别";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}


<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('Add_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span>添加合伙人用户</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>


        </form>
    </div>
    <table style=" min-height:150px;" id="table"
           data-toggle="table"
           data-url="GetPartnerUserClassesPage"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>

                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="name_mobile" data-sortable="true">姓名</th>
                <th data-field="type_str" data-sortable="true">类型</th>
                <th data-field="pay_money" data-sortable="true" data-formatter="formatting_money">支付金额</th>
                <th data-field="payment_str" data-sortable="true">支付类型</th>
                <th data-field="guarantee_money" data-sortable="true" data-formatter="formatting_money">保证金</th>
                <th data-field="g_product_num">交付课</th>
                <th data-field="s_product_num">精品课</th>
                <th data-field="x_product_num">学习券</th>
                <th data-field="state_str" data-sortable="true">状态</th>
                <th data-field="creation_date_str" data-sortable="true" data-formatter="formatting_date">创建时间</th>
                <th data-field="remark" data-sortable="true">备注说明</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>

            </tr>
        </thead>
    </table>



</div>
<script type="text/javascript">

    function UserHeadUrlFormatter(value, row, index) {
        return "<div>  <table>   <tr><td style='width:65px; '>  <img style='width:60px; ' src='" + row.image_url + "' /> </td>      <td>    <div>" + row.user_name + "【" + row.mobile + "】</div>       <div> " + row.nickname + "  &nbsp; " + row.real_name + "&nbsp; </div>   </td>        </table></div>"
    }

    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);

    }
    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function customer_head_img(value, row, index) {
        return "<img style='width:60px; ' src='" + value + "' />"
    }

    function queryParams(params) {

        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),

        }
    }
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }

    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'
        html = html + '      <li><a href="#" onclick="Resolution(' + row.classes_id + ')">拆分</a></li>'
        html = html + '      <li><a href="#" onclick="Edit(' + row.classes_id + ')">编辑</a></li>'
        html = html + '      <li><a href="#" onclick="UpdateState(' + row.classes_id + ')">禁用</a></li>'
        html = html + '      </ul>     </div>'
        return html;
    }
    //编辑
    function Edit(classes_id) {
        open_url("Edit_frm?classes_id=" + classes_id);
    }
    //删除、更改装填
    function UpdateState(classes_id) {
        show_confirm("是否确定禁用该条订单？", function (isok) {
            if (isok) {
                ajaxRequest("UpdateState", "get", { classes_id: classes_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "index?time" + new Date().toString();
                    }
                    else {
                        9
                        show_msg("失败。" + ret.Msg)
                    }
                });
            }

        }) 
    }

    //拆分订单
    function Resolution(classes_id) {
        show_confirm("是否确定拆分该条订单？", function (isok) {
            if (isok) {
                ajaxRequest("ResolutionOrder", "get", { classes_id: classes_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "index?time" + new Date().toString();
                    }
                    else {
                        9
                        show_msg("失败。" + ret.Msg)
                    }
                });
            }

        }) 
    }

</script>

