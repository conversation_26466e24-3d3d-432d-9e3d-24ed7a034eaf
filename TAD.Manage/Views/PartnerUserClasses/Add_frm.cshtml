@{
    ViewBag.Title = "添加联盟合伙人";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
<script>
    $(function () {
        $('#mobile_name').bind('input propertychange', function () {
            var mobile_name = $.trim($("#mobile_name").val());
            if (verify_mobile(mobile_name)) {

                ajaxRequest("LoadUser", "get", { mobile_name: mobile_name }, function (isok, ret) {
                    if (ret.Isok) {
                        $("#to_customer").css('display', 'block');
                        $("#to_customer_head_img").attr("src", ret.Obj.head_url);
                        $("#to_customer_name").text(ret.Obj.name);
                        $("#to_customer_mobile").text(ret.Obj.mobile);
                        $("#customer_id").val(ret.Obj.user_id);
                    }
                    else {
                        $("#to_customer").css('display', 'none');
                        $("#customer_id").val('');
                    }
                });
            }
            else {
                $("#to_customer").css('display', 'none');
                $("#customer_id").val('');
            }
        });
    })

    $(function () {
        $('#mobile_name_l').bind('input propertychange', function () {
            var mobile_name_l = $.trim($("#mobile_name_l").val());
            if (verify_mobile(mobile_name_l)) {

                ajaxRequest("LoadUser", "get", { mobile_name_l: mobile_name_l }, function (isok, ret) {
                    if (ret.Isok) {
                        $("#to_customer_l").css('display', 'block');
                        $("#to_customer_l_head_img").attr("src", ret.Obj.head_url);
                        $("#to_customer_l_name").text(ret.Obj.name);
                        $("#to_customer_l_mobile").text(ret.Obj.mobile);
                        $("#customer_id").val(ret.Obj.user_id);
                    }
                    else {
                        $("#to_customer_l").css('display', 'none');
                        $("#customer_id").val('');
                    }
                });
            }
            else {
                $("#to_customer_l").css('display', 'none');
                $("#customer_id").val('');
            }
        });
    })
</script>


@using (Ajax.BeginForm("Add", "PartnerUserClasses", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <div class="col-sm-9">
            @Html.Action("SearchUser", "SharedLump")
        </div>
    </div>

    <div class="form-group">
        <div class="col-sm-9">
            @Html.Action("SearchLecturer", "SharedLump")
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">辅导提成：</label>
        <div class="col-sm-9" style="width:120px;">
            <input style="width:90px;" type="number" class="form-control" data-val="true" value="10" name="lecturer_proportion">
            <span class="field-validation-valid" data-valmsg-for="lecturer_proportion" data-valmsg-replace="true"></span>
        </div>
        <div style="margin-top:10px;margin-left:-20px;">%</div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">类型：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type_id" id="type_id">
                <option value="0">请选择</option>
                @foreach (TAD.Model.partner_type item in (List<TAD.Model.partner_type>)(ViewData["partner_type"]))
                {
                    <option value="@item.type_id">@item.name</option>
                }
            </select>
        </div>
    </div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">支付金额：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="支付金额 是必需的。" name="pay_money" placeholder="请输入 支付金额">
            <span class="field-validation-valid" data-valmsg-for="pay_money" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">支付方式：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="payment" id="payment">
                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.payment)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.payment), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        <option value="@myCode">@strName</option>
                    }
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">保证金：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="保证金 是必需的。" name="guarantee_money" placeholder="请输入 保证金">
            <span class="field-validation-valid" data-valmsg-for="guarantee_money" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入 备注信息">

        </div>
    </div>
}
<script>
    function Save() {
        show_confirm("是否确定添加此条数据？", function (isok) {
            if (isok) {
                $("#form").submit();
            }

        })
       
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>


