@{
    ViewBag.Title = "添加精彩回顾";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}


@using (Ajax.BeginForm("AddCourseReview", "CourseReview", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">图片：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <table>
                    <tr>
                        <td>
                            <div onclick="choosefile('image_url')" style="width:200px;height:150px;border:solid 1px #808080;  background-image:url('/Content/images/upLoadbg.png');background-repeat:no-repeat; border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%" id="image_url_upload" src="" />
                                <input hidden="hidden" id="image_url" name="image_url" value="" />
                            </div>
                        </td>
                    </tr>

                </table>
            </div>
            <div class="layui-inline" style="padding-bottom:20px;padding-top:20px;">
                说明：1200px*600px像素的图片
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" value="1" name="sort" placeholder="请输入 序号">
            <span class="field-validation-valid" data-valmsg-for="sort" data-valmsg-replace="true"></span>
        </div>
    </div>
    
}

<div class="site-demo-upbar" hidden="hidden">
    <input type="file" name="file" class="layui-upload-file" id="file">
</div>

<script>
    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
         , method: 'post' //上传接口的http类型
              , before: function (input) {
                  show_loading();
              }
         , success: function (res) {
             close_loading();
             if (res.Isok) {
                 $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                 $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
             }
             else {
                 alert(res.Msg);
             }
         }
        });
    });

    var image_id;
    function choosefile(_image_id) {
        image_id = _image_id;
        var f = document.getElementById("file");
        f.click();
    }
</script>
<script>
    function Save() {

        //开始提交
        if (!isNullOrUndefined($("#image_url").val())) {
            $("#form").submit()
        }
        else {
            show_msg("图片不能为空。")
        }
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("添加成功！");
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>

