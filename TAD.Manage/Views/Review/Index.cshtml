@{
    ViewBag.Title = "精彩回顾";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

 @model List<TAD.Model.course_review>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('AddReview_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加图片</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">

                <thead>
                    <tr>
                        <th width="50">序号</th>
                        <th>图片</th>
                        <th>状态</th>
                        <th>排序</th>
                        <th>创建时间</th>
                        <th style="width:130px;">操作</th>
                    </tr>
                </thead>
                <tbody id="search_table">
                    @if (Model != null && Model.Count > 0)
                    {
                        int rowIndex = 0;
                        foreach (var item in Model)
                        {
                            rowIndex++;
                            <tr>
                                <td>@rowIndex</td>
                                <td><img style="width:50px;" src="@item.image_url" /> </td>
                                <td>
                                    @{
                            string state_str = item.state == true ? "启用" : "禁用";
                            <span>@state_str</span>
                                    }
                                </td>
                                <td>@item.sort</td>
                                <td>@item.creation_date.ToShortDateString()</td>
                                <td>
                                    <div class="btn-toolbar" role="toolbar">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-default" onclick="Edit('@item.review_id')">编辑</button>
                                            <button type="button" class="btn btn-default" onclick="Delete('@item.review_id')">删除</button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    //编辑
    function Edit(review_id) {
        open_url('EditReview_frm?review_id=' + review_id);
    }
    //删除
    function Delete(review_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("Delete", "get", { review_id: review_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "index?time" + new Date().toString();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }
</script>