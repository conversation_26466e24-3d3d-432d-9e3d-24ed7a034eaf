@{
    ViewBag.Title = "编辑卡包";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@model TAD.Model.partner_user_card
@using (Ajax.BeginForm("Edit", "PartnerType", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label class="col-sm-2 control-label">姓名：</label>
        <div class="col-sm-9">
            <input type="text" readonly="readonly" class="form-control"  placeholder="请输入 备注信息" value="@ViewBag.user_name">
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">来源：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="source" id="source">
                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.partner_user_card_source)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.partner_user_card_source), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        if (Model.source == myCode)
                        {
                            <option selected="selected" value="@myCode">@strName</option>
                        }
                        else
                        {
                            <option value="@myCode">@strName</option>
                        }
                    }
                }
            </select>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">用途：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="use" id="use">
                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.partner_user_card_use)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.partner_user_card_use), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        if (Model.use == myCode)
                        {
                            <option selected="selected" value="@myCode">@strName</option>
                        }
                        else
                        {
                            <option value="@myCode">@strName</option>
                        }
                    }
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">产品：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="product_id" id="product_id">
                <option value="0">请选择</option>
                @foreach (TAD.Model.partner_card_product item in (List<TAD.Model.partner_card_product>)(ViewData["partner_card_product"]))
                {
                    if (Model.product_id == item.product_id)
                    {
                        <option selected="selected" value="@item.product_id">@item.name</option>
                    }
                    else
                    {
                        <option value="@item.product_id">@item.name</option>

                    }
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">支付金额：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="支付金额 是必需的。" name="pay_money" placeholder="请输入 支付金额" value="@Model.pay_money">
            <span class="field-validation-valid" data-valmsg-for="pay_money" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">支付方式：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="payment" id="payment">
                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.payment)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.payment), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        if (Model.payment == myCode)
                        {
                            <option selected="selected" value="@myCode">@strName</option>
                        }
                        else
                        {
                            <option value="@myCode">@strName</option>
                        }
                    }
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.partner_user_card__state)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.partner_user_card__state), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        if (Model.use == myCode)
                        {
                            <option selected="selected" value="@myCode">@strName</option>
                        }
                        else
                        {
                            <option value="@myCode">@strName</option>
                        }
                    }
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入 备注信息" value="@Model.remark">

        </div>
    </div>
    <input value="@Model.card_id" name="card_id" hidden="hidden" id="card_id" />
}
<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>
