@{
    ViewBag.Title = "添加卡包";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
<script>
    $(function () {
        $('#mobile_name').bind('input propertychange', function () {
            var mobile_name = $.trim($("#mobile_name").val());
            if (verify_mobile(mobile_name)) {

                ajaxRequest("LoadUser", "get", { mobile_name: mobile_name }, function (isok, ret) {
                    if (ret.Isok) {
                        $("#to_customer").css('display', 'block');
                        $("#to_customer_head_img").attr("src", ret.Obj.head_url);
                        $("#to_customer_name").text(ret.Obj.name);
                        $("#to_customer_mobile").text(ret.Obj.mobile);
                        $("#customer_id").val(ret.Obj.user_id);
                    }
                    else {
                        $("#to_customer").css('display', 'none');
                        $("#customer_id").val('');
                    }
                });
            }
            else {
                $("#to_customer").css('display', 'none');
                $("#customer_id").val('');
            }
        });
    })
</script>


@using (Ajax.BeginForm("Add", "PartnerType", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{

    <div class="form-group">
        <div class="col-sm-9">
            @Html.Action("SearchUser", "SharedLump")
        </div>
    </div>



    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">产品：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="product_id" id="product_id">
                <option value="0">请选择</option>
                @foreach (TAD.Model.partner_card_product item in (List<TAD.Model.partner_card_product>)(ViewData["partner_card_product"]))
                {
                    <option value="@item.product_id">@item.name</option>
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">数量：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="数量 是必需的。" name="amount" placeholder="请输入 数量">
            <span class="field-validation-valid" data-valmsg-for="amount" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">支付金额：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="支付金额 是必需的。" name="pay_money" placeholder="请输入 支付金额">
            <span class="field-validation-valid" data-valmsg-for="pay_money" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">支付方式：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="payment" id="payment">
                @{
                    foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.payment)))
                    {
                        string strName = Enum.GetName(typeof(TAD.Model.Enum.payment), myCode);//获取名称
                        string strVaule = myCode.ToString();//获取值
                        <option value="@myCode">@strName</option>
                    }
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入 备注信息">

        </div>
    </div>
}
<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

</script>



