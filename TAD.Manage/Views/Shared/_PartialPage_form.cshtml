



<!DOCTYPE html>
<html>
<head>

    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    @{
        var data = new ViewDataDictionary();

        data.Add("import", @ViewBag.import);
        Html.RenderPartial("/Views/Shared/_PartialPage_Importing.cshtml", data);
    }

    <title>@ViewBag.Title</title>
</head>
<body>
    <div class="panel panel-default">
        <div class="panel-heading">
            <form class="form-inline">
                <button type="button" class="btn btn-success" onclick="self.location = document.referrer;" id="submitBTN"><span class="glyphicon  glyphicon-chevron-left"></span> 返回</button>
                @RenderSection("top_menu", false)
            </form>
        </div>
        <div class="panel-body">
            @RenderBody()
        </div>

    </div>
    <div class="panel-heading" style=" margin-top:-20px;">
        <form class="form-inline" style=" text-align:right;">
            @if (ViewBag.submit_txt == null)
            {
                <button type="button" class="btn btn-success" onclick="Save()" id="submitBTN"><span class="glyphicon glyphicon-ok"></span> 确定</button>
            }
            else
            {
                <button type="button" class="btn btn-success" onclick="Save()" id="submitBTN"><span class="glyphicon glyphicon-ok"></span>@ViewBag.submit_txt</button>
            }
            @RenderSection("footer", false)
        </form>
    </div>

</body>
</html>
