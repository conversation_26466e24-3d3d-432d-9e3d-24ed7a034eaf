@{
    ViewBag.Title = "编辑案例";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@model TAD.Model.course_student_case

@using (Ajax.BeginForm("EditCourseStudentCase", "CourseStudentCase", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">学员姓名：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="学员姓名 是必需的。" name="name" value="@Model.name" placeholder="请输入 学员姓名">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>

    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">学员公司：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="学员公司 是必需的。" name="company" value="@Model.company" placeholder="请输入 学员公司">
            <span class="field-validation-valid" data-valmsg-for="company" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">学员职位：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="学员职位 是必需的。" name="position" value="@Model.position" placeholder="请输入 学员职位">
            <span class="field-validation-valid" data-valmsg-for="position" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">学员头像：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <table>
                    <tr>
                        <td>
                            <div onclick="choosefile('image_url')" style="width:300px;height:330px; background-image:url('/Content/images/upLoadbg.png');background-repeat:no-repeat; border:1px solid #c3c2c2;border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%" id="image_url_upload" src="@Model.image_url" />
                                <input hidden="hidden" id="image_url" name="image_url" value="@Model.image_url" />
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="layui-inline">
                说明：300px*330px像素的图片
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">学员简介：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="学员简介 是必需的。" value="@Model.details" name="details" placeholder="请输入 学员简介">
            <span class="field-validation-valid" data-valmsg-for="details" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" value="@Model.sort" name="sort" placeholder="请输入 序号">
            <span class="field-validation-valid" data-valmsg-for="sort" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                @if (Model.state == true)
                {
                    <option value="true" selected="selected">启用</option>
                    <option value="false">禁用</option>
                }
                else
                {
                    <option value="true">启用</option>
                    <option value="false" selected="selected">禁用</option>
                }
            </select>
        </div>
    </div>
    <input hidden="hidden" id="case_id" name="case_id" type="number" value="@Model.case_id" />

}

<div class="site-demo-upbar" hidden="hidden">
    <input type="file" name="file" class="layui-upload-file" id="file">
</div>

<script>
    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
         , method: 'post' //上传接口的http类型
              , before: function (input) {
                  show_loading();
              }
         , success: function (res) {
             close_loading();
             if (res.Isok) {
                 $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                 $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
             }
             else {
                 alert(res.Msg);
             }
         }
        });
    });

    var image_id;
    function choosefile(_image_id) {
        image_id = _image_id;
        var f = document.getElementById("file");
        f.click();
    }
</script>
<script>
    function Save() {
        //开始提交
        if (!isNullOrUndefined($("#image_url").val())) {
            $("#form").submit()
        }
        else {
            show_msg("图片不能为空。")
        }
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            show_msg("添加成功！");
            window.location = "index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>
