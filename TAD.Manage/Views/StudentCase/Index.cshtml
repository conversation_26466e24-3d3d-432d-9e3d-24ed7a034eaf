@{
    ViewBag.Title = "成功案例";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('AddStudentCase_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span>添加</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>


        </form>
    </div>

    <table style=" min-height:150px;" id="table"
           data-toggle="table"
           data-url="GetCourseStudentCsePage"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="name" data-sortable="true">姓名</th>
                <th data-field="position" data-sortable="true">职位</th>
                <th data-field="company" data-sortable="true">公司</th>
                <th data-field="image_url" data-formatter="customer_head_img" data-sortable="true">头像</th>
                <th data-field="details" data-sortable="true">简介</th>
                <th data-field="state_str" data-sortable="true">状态</th>
                <th data-field="creation_date_str" data-formatter="formatting_date" data-sortable="true">创建时间</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
            </tr>
        </thead>
    </table>



</div>
<script type="text/javascript">
    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function customer_head_img(value, row, index) {
        return "<img style='width:60px; ' src='" + value + "' />"
    }

    function queryParams(params) {

        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),

        }
    }
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }

    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'
        html = html + '<li><a href="#" onclick="Edit(' + row.case_id + ')">编辑</a></li>'
        html = html + '      <li><a href="#" onclick="Delete(' + row.case_id + ')">删除</a></li>'
        html = html + '      </ul>     </div>'
        return html;
    }

    //编辑
    function Edit(case_id) {
        open_url('EditStudentCase_frm?case_id=' + case_id);
    }
    //删除
    function Delete(case_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("Delete", "get", { case_id: case_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "index?time" + new Date().toString();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }

</script>