@{
    ViewBag.Title = "在线视频列表";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
@{
    TAD.Model.course_online course_online = (TAD.Model.course_online)ViewBag.course_online;
}
@model List<TAD.Model.course_online_list>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <button type="button" class="btn btn-success" onclick="open_url('/Course/Oline_Index')" id="submitBTN"><span class="glyphicon  glyphicon-chevron-left"></span> 返回</button>
            <button type="button" class="btn btn-success" onclick="Add_online_video(@ViewBag.online_id)" id="submitBTN"><span class="	glyphicon glyphicon-plus"></span> 添加视频</button>
        </form>
    </div>
    <div class="panel-body">
        <blockquote class="layui-elem-quote">

            <span>【@course_online.title】文件列表如下：</span>

        </blockquote>
        <div class="table-responsive">
            <table class="table table-bordered table-hover">

                <thead>
                    <tr>
                        <th width="50">序号</th>
                        <th>标题</th>
                        <th>封面图</th>
                        <th>文件连接</th>
                        <th>文件类型</th>
                        <th>文件大小</th>
                        <th>文件时长</th>
                        <th>排序</th>
                        <th>是否免费</th>
                        <th>观看人数</th>
                        <th>创建日期</th>
                        <th style="width:130px;">操作</th>
                    </tr>
                </thead>
                <tbody id="search_table">
                    @if (Model.Count > 0)
                    {
                        int rowIndex = 0;
                        foreach (var item in Model)
                        {

                            rowIndex++;
                            <tr>
                                <td>@rowIndex</td>
                                <td>@item.title</td>
                                <td>
                                    @if (item.image_url == null)
                                    {
                                        <img style="width:50px;" src="~/Content/images/upLoadbg.png" />
                                    }
                                    else
                                    {
                                        <img style="width:50px;" src="@item.image_url" />
                                    }
                                </td>
                                <td>@item.file_url</td>
                                <td>
                                    @{
                            var file_type_str = ((TAD.Model.Enum.course_online_list_type)item.file_type).ToString();
                                    }
                                    <span>@file_type_str</span>
                                </td>
                                <td>@item.file_size M</td>
                                <td>@item.duration &nbsp;min</td>
                                <td>@item.sort_code</td>
                                <td>
                                    @if (item.is_free == true)
                                    {
                                        <span>免费</span>
                                    }
                                    else
                                    {
                                        <span>收费</span>
                                    }
                                </td>
                                <td>@item.view_num</td>
                                <td>@item.creation_date.ToShortDateString()</td>
                                <td>
                                    <div class="btn-toolbar" role="toolbar">
                                        <div class="btn-group">

                                            <button type="button" class="btn btn-default" onclick="Edit('@item.list_id')">编辑</button>

                                            <button type="button" class="btn btn-default" onclick="Delete('@item.list_id')">删除</button>
                                        </div>
                                    </div>
                                </td>
                            </tr>

                        }
                    }
                </tbody>
            </table>
        </div>

    </div>
</div>
<script type="text/javascript">
    //添加
    function Add_online_video(online_id) {
        open_url('AddCourseOnlineList_frm?online_id=' + online_id);
    }
    //编辑
    function Edit(list_id) {
        open_url('EditCourseOnlineList_frm?list_id=' + list_id);
    }
    //删除
    function Delete(list_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("DeleteCourseOnlineList", "get", { list_id: list_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "CourseOnlineList_Index?online_id= @ViewBag.online_id";
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }

</script>