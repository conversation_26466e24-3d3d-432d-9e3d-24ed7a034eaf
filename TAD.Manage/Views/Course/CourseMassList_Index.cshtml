@{
    ViewBag.Title = "群发列表";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

@model List<TAD.Manage.Models.CourseMass>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('/Course/Oline_Index')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 返回上一页</button>
                    </td>
                    <td>
                        <button style="margin-left:30px;" type="button" class="btn btn-success" onclick="OpenDeleteMass()" id="submitBTN">清空数据</button>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">

                <thead>
                    <tr>
                        <th width="50">序号</th>
                        <th data-field="type_name">所在分类</th>
                        <th data-field="title">视频标题</th>
                        <th data-field="lecturer_name">所属讲师</th>
                        <th data-field="image_url" data-formatter="imageFormatter">封面图片</th>
                        <th data-field="price" data-formatter="formatting_money">销售价格</th>
                        <th data-field="state_str">状态</th>
                        <th data-field="browse_num">浏览人数</th>
                        <th data-field="view_num">观看人数</th>
                        <th data-field="remark">备注</th>
                        <th data-field="creation_date_str" data-formatter="formattion_start_date">创建日期</th>
                        <th style="width:90px;">操作</th>
                    </tr>
                </thead>
                <tbody id="search_table">
                    @if (Model != null && Model.Count > 0)
                    {
                        int rowIndex = 0;
                        foreach (var item in Model)
                        {
                            rowIndex++;
                            <tr>
                                <td>@rowIndex</td>
                                <td>@item.course_type.name</td>
                                <td>@item.course_online.title</td>
                                <td>

                                    @{
                                        if (item.course_lecturer == null)
                                        {
                                            <span></span>
                                        }
                                        else
                                        {
                                            <span>@item.course_lecturer.name</span>
                                        }
                                    }

                                </td>
                                <td><img style="width:50px;" src="@item.course_online.image_url" /> </td>
                                <td>@item.course_online.price</td>
                                <td>
                                    @{
                                        string state_str = item.course_mass.state == 1 ? "启用" : "禁用";
                                    }
                                    <span>@state_str</span>
                                </td>
                                <td>@item.course_online.browse_num</td>
                                <td>@item.course_online.view_num</td>
                                <td>@item.course_online.remark</td>
                                <td>@item.course_mass.creation_date.ToShortDateString()</td>
                                <td>
                                    <div class="btn-toolbar" role="toolbar">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-default" onclick="Delete('@item.course_mass.course_mass_id')">删除</button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
    <div onclick="OpenSentMass()"><button type="button" class="btn btn-primary btn-xl btn-block" style="font-size:20px;">确定群发</button></div>
</div>
<script type="text/javascript">

    //删除
    function Delete(course_mass_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("DeleteCourseMass", "get", { course_mass_id: course_mass_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "CourseMassList_Index?time" + new Date().toString();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }

    //确定群发
    function OpenSentMass() {
        show_confirm("是否确定群发所有数据？", function (isok) {
            if (isok) {
            }
            //if (isok) {
            //    ajaxRequest("DeleteCourseMass", "get", { course_mass_id: course_mass_id }, function (isok, ret) {
            //        if (ret.Isok) {
            //            window.location = "CourseMassList_Index?time" + new Date().toString();
            //        }
            //        else {
            //            show_msg("删除失败。" + ret.Msg)
            //        }
            //    });
            //}

        })
    }
    //清空所有数据
    function OpenDeleteMass() {
        show_confirm("是否确定清空所有数据？", function (isok) {
            if (isok) {
            }
            //if (isok) {
            //    ajaxRequest("DeleteCourseMass", "get", { course_mass_id: course_mass_id }, function (isok, ret) {
            //        if (ret.Isok) {
            //            window.location = "CourseMassList_Index?time" + new Date().toString();
            //        }
            //        else {
            //            show_msg("删除失败。" + ret.Msg)
            //        }
            //    });
            //}

        })
    }

</script>