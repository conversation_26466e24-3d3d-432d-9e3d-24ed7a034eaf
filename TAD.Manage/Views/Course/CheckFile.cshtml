@{
    ViewBag.Title = "CheckFile";
}
<style>
    table, th, td
  {
  border: 1px solid blue;
  }
</style>
@model List<TAD.Manage.Models.CourseCheckFile>
<table style="width:100%;">
    <thead>
        <tr>
            <td>
                online_id
            </td>
            <td>
                list_id
            </td>
            <td>
                online_title
            </td>
            <td>
                list_title
            </td>
            <td>
                file_url
            </td>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @item.online_id
                </td>
                <td>@item.list_id</td>

                <td>@item.online_title</td>
                <td>
                    @item.list_title
                </td>

                <td>@item.file_url</td>
            </tr>
        }
    </tbody>
</table>
