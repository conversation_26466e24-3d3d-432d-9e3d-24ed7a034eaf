@{
    ViewBag.Title = "编辑在线视频";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
@{
    TAD.Model.course_online course_online = (TAD.Model.course_online)ViewBag.course_online;
}
@model TAD.Model.course_online_list
@using (Ajax.BeginForm("EditCourseOnlineList", "Course", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <blockquote class="layui-elem-quote">

        <span>编辑【@course_online.title】视频：</span>

    </blockquote>
    if (Model != null)
    {
        <div class="form-group">
            <label class="col-sm-2 control-label">标题：</label>
            <div class="col-sm-9">
                <input type="text" class="form-control" data-val="true" data-val-required="标题 是必需的。" name="title" placeholder="请输入标题" value="@Model.title">
                <span class="field-validation-valid" data-valmsg-for="title" data-valmsg-replace="true"></span>
            </div>
        </div>


        <div class="form-group">
            <label for="inputPassword" class="col-sm-2 control-label">封面图片：</label>
            <div class="col-sm-9">
                <div class="layui-inline">
                    <table>
                        <tr>
                            <td>
                                <div onclick="choosefile('image_url')" style="width:341px ;height:180px;  background-image:url('/Content/images/upLoadbg.png');background-repeat:no-repeat; border:1px solid #c3c2c2;border-radius:5px; background-position:center center; background-size:50%;">
                                    <img style="width:100%;" id="image_url_upload" src="@Model.image_url" />
                                    <input hidden="hidden" id="image_url" name="image_url" value="@Model.image_url" />
                                </div>
                            </td>
                        </tr>

                    </table>
                </div>
                <div class="layui-inline">
                    说明：720px*380px像素的图片
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="inputPassword" class="col-sm-2 control-label">课程简介：</label>
            <div class="col-sm-9">
                <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.config.js"></script>
                <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.all.min.js"></script>

                <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/lang/zh-cn/zh-cn.js"></script>
                <script id="editor" type="text/plain" style="width:690px;height:310px;">

                </script>

                <script type="text/javascript">
                    function getContent() {
                        return UE.getEditor('editor').getContent();
                    }

                    //实例化编辑器
                    //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
                    var ue = UE.getEditor('editor');
                    ue.ready(function () {
                        UE.getEditor('editor').setContent('@Html.Raw(Model.introduce)', false);
                    });
                </script>
            </div>
        </div>
        <textarea type="text" hidden="hidden" id="introduce" name="introduce">@Model.introduce</textarea>

        <div class="form-group">
            <label for="inputPassword" class="col-sm-2 control-label">资源类型：</label>
            <div class="col-sm-9">
                <div class="layui-inline">
                    <select class="combobox form-control" name="file_type" id="file_type">
                        @foreach (int myCode in Enum.GetValues(typeof(TAD.Model.Enum.course_online_list_type)))
                        {
                            string strName = Enum.GetName(typeof(TAD.Model.Enum.course_online_list_type), myCode);//获取名称
                            string strVaule = myCode.ToString();//获取值
                            if (Model.file_type == myCode)
                            {
                                <option selected="selected" value="@strVaule">@strName</option>
                            }
                            else
                            {
                                <option value="@strVaule">@strName</option>
                            }
                        }
                    </select>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">资源时长：</label>
            <div class="col-sm-9">
                <div>
                    <input type="text" class="form-control" data-val="true" data-val-required="资源时长 是必需的。" id="duration" name="duration" placeholder="点击右侧按钮获取" style="width:12%;float:left" value="@Model.duration">
                    <div style="float:left;margin-top:8px;margin-left:3px;font-size:16px;">秒</div><input onclick="GetVideoDuration('video_file')" type="button" value="获取时长" style="float:left;background:#3ec107;color:#ffffff;padding:5px;width:100px;margin-left:10px;border-radius:5px;" />
                </div>

                <video style="display:none;" oncanplaythrough="videoDurationEd(this)" controls="controls" id="video_file"></video>
                <span class="field-validation-valid" data-valmsg-for="duration" data-valmsg-replace="true"></span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">文件链接：</label>
            <div class="col-sm-9">
                <input type="text" class="form-control" data-val="true" data-val-required="文件链接 是必需的。" id="file_url" name="file_url" placeholder="请输入 文件链接" value="@Model.file_url">
                <span class="field-validation-valid" data-valmsg-for="file_url" data-valmsg-replace="true"></span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">文件大小：</label>
            <div class="col-sm-9">
                <div>
                    <input type="text" class="form-control" data-val="true" data-val-required="文件大小 是必需的。" id="file_size" name="file_size" placeholder="点击右侧按钮获取" style="width:10%;float:left" value="@Model.file_size">
                    <div style="float:left;margin-top:8px;margin-left:3px;font-size:16px;">MB</div><input onclick="openSize()" type="button" value="获取视频大小" style="float:left;background:#3ec107;color:#ffffff;padding:5px;width:100px;margin-left:10px;border-radius:5px;" />
                </div>

                <span class="field-validation-valid" data-valmsg-for="file_size" data-valmsg-replace="true"></span>
            </div>

        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">观看次数：</label>
            <div class="col-sm-9">
                <input type="number" class="form-control" name="view_num" placeholder="请输入 观看次数" value="@Model.view_num">
                <span class="field-validation-valid" data-valmsg-for="view_num" data-valmsg-replace="true"></span>
            </div>
        </div>
        <div class="form-group">
            <label for="inputPassword" class="col-sm-2 control-label">是否免费：</label>
            <div class="col-sm-9">
                <select class="combobox form-control" name="is_free" id="is_free">
                    @if (Model.is_free == true)
                    {
                        <option value="true" selected="selected">是</option>
                        <option value="false">否</option>
                    }
                    else
                    {
                        <option value="true">是</option>
                        <option value="false" selected="selected">否</option>
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">排序：</label>
            <div class="col-sm-9">
                <input type="number" class="form-control" data-val="true" name="sort_code" value="@Model.sort_code" placeholder="请输入排序">
            </div>
        </div>
        <input hidden="hidden" id="online_id" name="online_id" value="@course_online.online_id" />
    }
    else
    {
        <div style="text-align:center;">暂无任何视频</div>
    }
    <input type="text" value="@Model.list_id" id="list_id" name="list_id" hidden="hidden" />
}

<div class="site-demo-upbar" hidden="hidden">
    <input type="file" name="file" class="layui-upload-file" id="file">
</div>

<script>

    $('input[name="file_url"]').on('input propertychange', function () {
        openSize();
        GetVideoDuration('video_file')
        autoSelectFileType($(this).val())

    })
    function autoSelectFileType(file_url) {
        file_url = file_url.toLowerCase();

        var is_video = true;
        if (file_url.indexOf(".mp3") != -1) {
            is_video = false;
        }
        else if (file_url.indexOf(".m4a") != -1) {
            is_video = false;
        }
        else if (file_url.indexOf(".wav") != -1) {
            is_video = false;

        }
        else if (file_url.indexOf(".wma") != -1) {
            is_video = false;

        }
        else if (file_url.indexOf(".midi") != -1) {
            is_video = false;
        }
        if (is_video) {
            $("#file_type").val(1);
        }
        else {
            $("#file_type").val(2);
        }
    }

    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
            , method: 'post' //上传接口的http类型
            , before: function (input) {
                show_loading();
            }
            , success: function (res) {
                close_loading();
                if (res.Isok) {
                    $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                    $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
                }
                else {
                    alert(res.Msg);
                }
            }
        });
    });

    var image_id;
    function choosefile(_image_id) {
        image_id = _image_id;
        var f = document.getElementById("file");
        f.click();
    }


    //获取视频大小
    function openSize() {
        var file_url = $("#file_url").val();
        if (isNullOrUndefined(file_url)) {
            alert("请输入文件路径！");
            return;
        }
        var url_size = $("#file_url").val();

        ajaxRequest("GetOnlineVideoSize", "get", { url_size: url_size }, function (isok, ret) {
            if (ret.Isok) {
                $("#file_size").val(ret.Obj);
            }

        });
    }

</script>

<script>
   
    function Save() {
        var introduction = UE.getEditor('editor').getContent();
        $("#introduce").val(introduction);
            $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "CourseOnlineList_Index?online_id="+@course_online.online_id;
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }

    function GetVideoDuration(file_id)
    {
        var file_url = $("#file_url").val();
        if (isNullOrUndefined(file_url)) {
            alert("请输入文件路径！");
            return;
        }

        document.getElementById(file_id).src = file_url;
    
    }
    function videoDurationEd(ele) {
        var hour = parseInt((ele.duration) / 3600);
        var minute = parseInt((ele.duration % 3600)/ 60);
        var num=minute;
        if(hour>0)
            num=num+(hour*60);
        //  var second = Math.ceil(ele.duration % 60);
        $("#duration").val(parseInt(ele.duration));
        //console.log(Math.floor(ele.duration));
        //document.write("这段视频的时长为："+hour+"小时，"+minute+"分，"+second+"秒");
        // document.getElementById("getDuration").innerHTML = "这段视频的时长为：" + hour + "小时，" + minute + "分，" + second + "秒";
    }
</script>