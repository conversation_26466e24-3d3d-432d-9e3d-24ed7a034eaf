@{
    ViewBag.Title = "在线视频";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}


<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('AddCourseOnLine_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加视频</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                    @*<td>
                        <button  style="margin-left:50px;" type="button" class="btn btn-success" onclick="open_url('CourseMassList_Index')" id="submitBTN">群发列表</button>
                    </td>*@
                    @*<td>
            <button style="margin-left:20px;" type="button" class="btn btn-success" onclick="open_url('/Course/CheckFile')" id="submitBTN">检查视频链接</button>
        </td>*@
                </tr>
            </table>
        </form>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <div class="table-responsive">
                <table style=" min-height:300px;" id="table"
                       data-toggle="table"
                       data-url="GetCourseList"
                       data-page-size="50"
                       data-side-pagination="server"
                       data-toolbar="#toolbar"
                       data-pagination="true"
                       data-page-list="[50,100,150]"
                       data-cookie="true"
                       data-cookie-id-table="saveId"
                       data-query-params="queryParams">
                    <thead>
                        <tr>
                            <th data-field="index" data-formatter="indexFormatter" data-width="20">序号</th>
                            <th data-field="type_name">所在分类</th>
                            <th data-field="tag_label">标签</th>
                            <th data-field="title">视频标题</th>
                            @*<th data-field="lecturer_name">所属讲师</th>*@
                            <th data-field="image_url" data-formatter="imageFormatter">封面图片</th>
                            @*<th data-field="price" data-formatter="formatting_money">销售价格</th>*@
                            @*<th data-field="commission" data-formatter="formatting_money">提成比例%</th>*@
                            <th data-field="state_type_str">状态</th>

                            <th data-field="browse_num">浏览人数</th>
                            <th data-field="view_num">观看人数</th>
                            @*<th data-field="remark">备注</th>*@
                            <th data-field="creation_date_str" data-formatter="formattion_start_date">创建日期</th>
                            <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">

    function formatting_money(value, row, index) {
        return formatCurrency(value);
    }
    function formattion_start_date(value, row, index) {

        return formatDate(value);
    }
    function imageFormatter(value, row, index) {
        if (value == null) {
            value = "";
        }
        return "<img style='width:60px;' src='" + value + "?x-oss-process=image/resize,h_30' />"
    }

    function indexFormatter(value, row, index) {
        return index + 1;
    }

    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }

    function operation_menu(value, row, index) {
        var html = "";
        html = html + '<div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown"> 操作 <span class="caret"></span></button><ul class="dropdown-menu">'
        html = html + "<li onclick=\"Edit('" + row.online_id + "' )\"> <a href='#'>编辑</a></li>"
        html = html + "<li onclick=\"Delete('" + row.online_id + "' )\"> <a href='#'>删除</a></li>"
        html = html + "<li onclick=\"open_course_list('" + row.online_id + "')\"> <a href='#'>文件列表</a></li>"
        html = html + "<li onclick=\"open_course_mass_list('" + row.online_id + "')\"> <a href='#'>添加群发</a></li>"
        html = html + "<li onclick=\"alert('" + row.online_id + "')\"> <a href='#'>查看ID</a></li>"
        html = html + "<li onclick=\"Set_label('" + row.online_id + "')\"> <a href='#'>设置标签</a></li>"
        html = html + '</ul></div>'
        return html;
    }

    function queryParams(params) {
        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val()
        }
    }

    //编辑
    function Edit(online_id) {
        open_url('EditCourseOnLine_frm?online_id=' + online_id);
    }
    //删除
    function Delete(online_id) {
        show_confirm("是否删除？", function (isok) {
            if (isok) {
                ajaxRequest("DeleteCourseOnLine", "get", { online_id: online_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "Oline_Index?time" + new Date().toString();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }
    function open_course_list(online_id) {
        open_url("/Course/CourseOnlineList_Index?online_id=" + online_id);
    }

    //添加到群发列表
    function open_course_mass_list(online_id) {
        ajaxRequest("AddCourseMassList", "post", { online_id: online_id }, function (isok, ret) {
            if (ret.Tag == false) {
                show_msg("添加失败：" + ret.Msg);
                setTimeout(function () {
                    open_url("/Course/CourseMassList_Index");

                }, 2000);
            }
            else if (ret.Isok == false) {
              
                show_msg("添加失败：" + ret.Msg);
            }
            
        })
    }

    //设置标签
    function Set_label(online_id) {
        open_url('SetLabel_frm?online_id=' + online_id);
    }
</script>
