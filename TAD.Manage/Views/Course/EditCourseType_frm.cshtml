@{
    ViewBag.Title = "编辑课程分类";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@model TAD.Model.course_type
@using (Ajax.BeginForm("EditCourseType", "Course", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    if (Model != null)
    {
        <div class="form-group">
            <label class="col-sm-2 control-label">分类名称：</label>
            <div class="col-sm-9">
                <input type="text" class="form-control" data-val="true" data-val-required="分类名称 是必需的。" name="name" value="@Model.name" placeholder="请输入分类名称">
                <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">排序：</label>
            <div class="col-sm-9">
                <input type="number" data-val="true" data-val-required="排序 是必需的。" class="form-control" name="sort" value="@Model.sort" placeholder="数值越大越靠前">
                <span class="field-validation-valid" data-valmsg-for="sort" data-valmsg-replace="true"></span>
            </div>
        </div>
        <input type="text" value="@Model.type_id" name="type_id" id="type_id" hidden="hidden" />
    }
}

<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "CourseType?time=" + new Date().toString();
        }
        else {
            show_msg("修改失败" + data.Msg)
        }
    }

</script>



