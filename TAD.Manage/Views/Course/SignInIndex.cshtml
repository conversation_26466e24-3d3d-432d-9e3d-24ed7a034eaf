@{
    ViewBag.Title = "签到管理";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}

<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>

                    <td>
                        <div style="margin-left:14px;">
                            <select class="combobox input-large form-control" onchange="select_type()" id="display_type">
                                <option value="-1">选择课程</option>
                                @foreach (TAD.Model.course_schedule item in (List<TAD.Model.course_schedule>)(ViewData["course_schedule"]))
                                {
                                    var date_time = TAD.Utility.DateUtil.kakeDate(item.start_datetime, item.finish_datetime);
                                <option value="@item.schedule_id">@item.title @date_time</option>
                                }
                            </select>
                        </div>
                    </td>
                    <td>
                        <div>
                            <button onclick="ExportExcel()"  style="margin-left:10px;" type="button" class="btn btn-success">导出Excel</button>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>

    <table style=" min-height:100px;" id="table"
           data-toggle="table"
           data-url="GetSigninPage"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter" data-width="20">序号</th>
                <th data-field="name_mobile">签到人</th>
                <th data-field="name_parent">推荐人</th>
                <th data-field="schedule_name">课程名称</th>
                <th data-field="signin_date_str" data-formatter="formatting_date">签到日期</th>
                <th data-field="code">签到码</th>
                <th data-field="schedule_time_str">开课时间</th>
                <th data-field="schedule_address_str">开课地点</th>
                <th data-field="creation_date_str" data-formatter="formatting_date">创建日期</th>
                <th data-field="remark">备注说明</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
            </tr>
        </thead>
    </table>
</div>


@*分页js*@
<script type="text/javascript">

    //下拉选择课程
    function select_type() {

        var display_type = $("#display_type").val();
        var $table = $('#table');
        $table.bootstrapTable('selectPage', 1);
        loadData();

    }
    $(function () {
        var display_type = getUrlParam("display_type");
        if (isNullOrUndefined(display_type)) {
            display_type = -1;
        }
        if (!isNullOrUndefined(display_type)) {
            $("#display_type").val(display_type);
        }

    });



    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);
    }

    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function queryParams(params) {
        var display_type = getUrlParam("display_type");
        if (isNullOrUndefined(display_type)) {
            display_type = -1;
        } 
        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),
            display_type: display_type,
        }
    }

    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }

    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'

        html = html + "<li onclick=\"add_remark('" + row.signin_id + "','" + row.remark + "','" + row.name + "' )\"> <a href='#'>备注</a></li>"



        html = html + '      </ul>     </div>'
        return html;
    }
    //备注
    function add_remark(signin_id, oldremark, name) {
        var display_type = getUrlParam("display_type");
        show_prompt("请输入备注信息", oldremark, function (value) {
            ajaxRequest("add_remark", "post", { signin_id: signin_id, remark: value }, function (isok, ret) {
                if (isok) {
                    if (ret.Isok) {
                        window.location.reload();
                    }
                }
            });
        });
    }

    function loadData() {
        var date = $("#date").val();
        var display_type = $("#display_type").val();

        window.location.href = "SignInIndex?display_type=" + display_type;
    }

    //导出Excel
    function ExportExcel() { 

       
        var course_schedule_id = $("#display_type").val();

        show_loading();
        ajaxRequest("ExportUnderAll", "get", { course_schedule_id: course_schedule_id }, function (isok, ret) {
            close_loading();
            if (ret.Isok) {
                open_url(ret.Obj);
            } else {
                alert("导出失败：" + ret.Msg)
            }
        });


    }
</script>
