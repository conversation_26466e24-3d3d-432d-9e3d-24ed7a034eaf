@{
    ViewBag.Title = "添加子类";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}



@using (Ajax.BeginForm("AddCoutseTypeTag", "Course", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label class="col-sm-2 control-label">选择大类：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="main_id" id="main_id">
                @foreach (TAD.Model.course_type_main item in (List<TAD.Model.course_type_main>)(ViewData["course_type_main"]))
                {
                    <option value="@item.main_id">@item.name</option>
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">选择子类：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type_id" id="type_id">
                @foreach (TAD.Model.course_type item in (List<TAD.Model.course_type>)(ViewData["course_type"]))
                {
                    <option value="@item.type_id">@item.name</option>
                }
            </select>
        </div>
    </div>

}


<script>
    function Save() {

        $("#form").submit()

    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "CourseTypeTag_Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>
