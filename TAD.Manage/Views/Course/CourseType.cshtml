@{
    ViewBag.Title = "课程分类";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
@model List<TAD.Manage.Models.CourseTypeCount>
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('AddCourseType_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span>添加分类</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">

                <thead>
                    <tr>
                        <th width="50">序号</th>
                        <th>分类名称</th>
                        <td>课程数量</td>
                        <th>排序</th>
                        <th>创建时间</th>
                        <th style="width:130px;">操作</th>
                    </tr>
                </thead>
                <tbody id="search_table">
                    @if (Model != null)
                    {
                        int rowIndex = 0;
                        foreach (var item in Model)
                        {
                            rowIndex++;
                            <tr>
                                <td>@rowIndex</td>
                                <td>@item.course_type.name</td>
                                <td>@item.count</td>
                                <td>@item.course_type.sort</td>
                                <td>@item.course_type.creation_date</td>
                            <td>
                                <div class="btn-toolbar" role="toolbar">
                                    <div class="btn-group">


                                        <button type="button" class="btn btn-default" onclick="Edit('@item.course_type.type_id')">编辑</button>


                                        <button type="button" class="btn btn-default" onclick="Delete('@item.course_type.type_id')">删除</button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    }
                }

                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/javascript">
    //编辑
    function Edit(type_id) {
        open_url('EditCourseType_frm?type_id=' + type_id);
    }
    //删除
    function Delete(type_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("Delete", "get", { type_id: type_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "CourseType?time" + new Date().toString();
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }
</script>
