@{
    ViewBag.Title = "编辑子类";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}

@model TAD.Model.course_type_tag

@using (Ajax.BeginForm("EditCourseTypeTag", "Course", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    if (Model != null)
    {
        <div class="form-group">
            <label class="col-sm-2 control-label">选择大类：</label>
            <div class="col-sm-9">
                <select class="combobox form-control" name="main_id" id="main_id">
                    @foreach (TAD.Model.course_type_main item in (List<TAD.Model.course_type_main>)(ViewData["course_type_main"]))
                    {
                        if (Model.main_id == item.main_id)
                        {
                            <option selected="selected" value="@item.main_id">@item.name</option>
                        }
                        else
                        {
                            <option value="@item.main_id">@item.name</option>
                        }

                    }
                </select>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">选择子类：</label>
            <div class="col-sm-9">
                <select class="combobox form-control" name="type_id" id="type_id">
                    @foreach (TAD.Model.course_type item in (List<TAD.Model.course_type>)(ViewData["course_type"]))
                    {
                        if (Model.type_id == item.type_id)
                        {
                            <option selected="selected" value="@item.type_id">@item.name</option>
                        }
                        else
                        {
                            <option value="@item.type_id">@item.name</option>
                        }

                    }
                </select>
            </div>
        </div>
        <input id="tag_id" value="@Model.tag_id" name="tag_id" hidden="hidden"/>

    }

}
<script>
    function Save() {
        $("#form").submit()
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "CourseTypeTag_Index?time=" + new Date().toString();
        }
        else {
            show_msg("编辑失败" + data.Msg)
        }
    }
</script>
