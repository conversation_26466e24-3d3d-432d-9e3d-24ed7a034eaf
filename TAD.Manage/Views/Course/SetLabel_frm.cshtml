@{
    ViewBag.Title = "设置标签";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}
 

@model List<TAD.Model.course_online_label_tag>
 
@using (Ajax.BeginForm("", "Course", new AjaxOptions { HttpMethod = "Post", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">课程名称：</label>
        <div class="col-sm-9">
            <input readonly="readonly" type="text" class="form-control" data-val="true" id="role_name" name="role_name" value="@ViewBag.course_name">
            <span class="field-validation-valid" data-valmsg-for="name" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">标签选择：</label>
        <div class="col-sm-9">

            @foreach (TAD.Model.course_online_label item in (List<TAD.Model.course_online_label>)(ViewData["course_online_label"]))
            {
                <div class="checkbox">
                    @if (Model.Where(o => o.label_id == item.label_id).FirstOrDefault() == null)
                    {
                        <input id="@item.label_id" name="action" value="@item.label_id" type="checkbox">@item.name
                    }
                    else
                    {
                        <input id="@item.label_id" name="action" value="@item.label_id" type="checkbox" checked="checked">@item.name
                    }
                </div>
            }
        </div>
    </div>
}

<script>
    function Save() {
        var online_id = getUrlParam('online_id');
        var action_str = "";
        $('input[name="action"]:checked').each(function () {//遍历每一个名字为interest的复选框，其中选中的执行函数
            // action_list.push($(this).val());//将选中的值添加到数组chk_value中
            action_str = action_str + "," + $(this).val()
        });
        if (action_str == "") {
            alert("最少选择一个功能！");
            return;
        } else {
            show_loading();
            ajaxRequest_simple("../Course/EditLabel_frm", { online_id: online_id, action_str: action_str }, function (isok, ret) {
                close_loading();
                if (ret.Isok) {
                    window.location = "Oline_Index?time=" + new Date().toString();
                } else {
                    alert("添加失败：" + ret.Msg)
                }
            });
        }
    }

</script>