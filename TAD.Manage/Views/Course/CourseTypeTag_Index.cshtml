@{
    ViewBag.Title = "课程大类";
    Layout = "~/Views/Shared/_Layout.cshtml.cshtml";
}
<div class="panel panel-default">
    <div class="panel-heading">
        <form class="form-inline">
            <table>
                <tr>
                    <td>
                        <button type="button" class="btn btn-success" onclick="open_url('AddCoutseTypeTag_frm')" id="submitBTN"><span class="glyphicon glyphicon-plus"></span> 添加子分类</button>
                    </td>
                    <td style="width:10px;"></td>
                    <td>
                        <div class="input-group">
                            <input type="text" id="search_text" class="form-control">
                            <span id="search_button" onclick="search_button()" style="cursor:pointer" class="input-group-addon">搜索</span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </div>

    <table style=" min-height:150px;" id="table"
           data-toggle="table"
           data-url="GetCourseTypeTagPage"
           data-page-size="50"
           data-side-pagination="server"
           data-toolbar="#toolbar"
           data-pagination="true"
           data-page-list="[50, 100,150]"
           data-cookie="true"
           data-cookie-id-table="saveId"
           data-query-params="queryParams"
           data-show-export="true">
        <thead>
            <tr>
                <th data-field="index" data-formatter="indexFormatter">ID</th>
                <th data-field="main_name" data-sortable="true">大类名称</th>
                <th data-field="course_name" data-sortable="true">子类名称</th>
                <th data-field="" data-formatter="operation_menu" data-sortable="true">操作</th>
            </tr>
        </thead>
    </table>

</div>
<script type="text/javascript">
    function formatting_date(value, row, index) {

        return formatDate(value);
    }
    function formatting_money(value, row, index) {
        return formatCurrency(value);

    }
    function indexFormatter(value, row, index) {
        return index + 1;
    }
    function customer_head_img(value, row, index) {
        return "<img style='width:60px; ' src='" + value + "' />"
    }

    function queryParams(params) {

        return {
            //每页多少条数据
            order: params.order,
            //请求第几页
            offset: params.offset,
            limit: params.limit,
            key: $('#search_text').val(),

        }
    }
    function search_button() {
        var $table = $('#table');
        $table.bootstrapTable('refresh');
    }

    function operation_menu(value, row, index) {
        var html = "";
        html = html + '  <div class="btn-group">  <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">      操作 <span class="caret"></span>  </button>    <ul class="dropdown-menu">'
        html = html + '<li><a href="#" onclick="Edit(' + row.tag_id + ')">编辑</a></li>'
        html = html + '      <li><a href="#" onclick="Delete(' + row.tag_id + ')">删除</a></li>'
        html = html + '      </ul>     </div>'
        return html;
    }

    //编辑
    function Edit(tag_id) {
        open_url('EditCourseTypeTag_frm?tag_id=' + tag_id);
    }
    //删除
    function Delete(tag_id) {
        show_confirm("是否确定删除该条数据？", function (isok) {
            if (isok) {
                ajaxRequest("DeleteCourseTypeTag", "get", { tag_id: tag_id }, function (isok, ret) {
                    if (ret.Isok) {
                        window.location = "CourseTypeTag_Index";
                    }
                    else {
                        show_msg("删除失败。" + ret.Msg)
                    }
                });
            }

        })
    }

</script>

