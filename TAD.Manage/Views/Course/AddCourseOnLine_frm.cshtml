@{
    /**/

    /**/

    ViewBag.Title = "添加在线视频";
    Layout = "~/Views/Shared/_PartialPage_form.cshtml";
}


@using (Ajax.BeginForm("AddCourseOnLine", "Course", new AjaxOptions { HttpMethod = "Post", OnBegin = "OnBegin", OnSuccess = "OnSuccess" }, new { id = "form", name = "form", @class = "form-horizontal", role = "form", style = " padding-top:10px;" }))
{
    <div class="form-group">
        <label class="col-sm-2 control-label">视频分类：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="type_id" id="type_id">
                @foreach (TAD.Model.course_type item in (List<TAD.Model.course_type>)(ViewData["course_type"]))
                {
                    <option value="@item.type_id">@item.name</option>
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">视频标题：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" data-val="true" data-val-required="视频标题 是必需的。" name="title" placeholder="请输入 视频标题">
            <span class="field-validation-valid" data-valmsg-for="title" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">副标题：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="subheading" placeholder="请输入 副标题">
            <span class="field-validation-valid" data-valmsg-for="subheading" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">课程讲师：</label>
        <div class="col-sm-9">

            <select class="combobox form-control" name="lecturer_id" id="lecturer_id">
                <option value="-1">暂未设置讲师</option>
                @foreach (TAD.Model.course_lecturer item in (List<TAD.Model.course_lecturer>)(ViewData["course_lecturer"]))
                {
                    <option value="@item.lecturer_id">@item.name</option>
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">所属机构：</label>
        <div class="col-sm-9">

            <select class="combobox form-control" name="organization_id" id="organization_id">
                @foreach (TAD.Model.syst_organization item in (List<TAD.Model.syst_organization>)(ViewData["syst_organization"]))
                {
                    <option value="@item.organization_id">@item.name</option>
                }
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">销售价格：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="销售价格 是必需的。" name="price" placeholder="请输入 销售价格">
            <span class="field-validation-valid" data-valmsg-for="price" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">预计总节数：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="预计总节数 是必需的。" value="0" name="predict_count" placeholder="请输入 预计总节数">
            <span class="field-validation-valid" data-valmsg-for="predict_count" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">提成比例（%）：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="提成比例 是必需的。" name="commission" placeholder="请输入 提成比例" value="0">
            <span class="field-validation-valid" data-valmsg-for="commission" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">积分比例（%）：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="integral_max" placeholder="请输入 积分比例" value="0">
            <span class="field-validation-valid" data-valmsg-for="integral_max" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">是否免费：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="is_free" id="is_free">
                <option value="true">免费</option>
                <option value="false" selected="selected">收费</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">是否精选：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="is_hot" id="is_hot">
                <option value="true">是</option>
                <option value="false" selected="selected">否</option>
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">VIP打折（Off %）：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="会员VIP打折 是必需的。" name="vip_discount" placeholder="请输入 会员VIP打折" value="100">
            <span class="field-validation-valid" data-valmsg-for="vip_discount" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">状态：</label>
        <div class="col-sm-9">
            <select class="combobox form-control" name="state" id="state">
                <option value="0" selected="selected">禁用</option>
                <option value="1">启用</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">浏览人数：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="browse_num" placeholder="请输入 浏览人数" value="0">
            <span class="field-validation-valid" data-valmsg-for="browse_num" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">观看次数：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" name="view_num" placeholder="请输入 观看次数" value="0">
            <span class="field-validation-valid" data-valmsg-for="view_num" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">排序：</label>
        <div class="col-sm-9">
            <input type="number" class="form-control" data-val="true" data-val-required="排序号数 是必需的。" value="0" name="sort_code" placeholder="请输入 排序号数">
            <span class="field-validation-valid" data-valmsg-for="sort_code" data-valmsg-replace="true"></span>
        </div>
    </div>
    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">封面图片：</label>
        <div class="col-sm-9">
            <div class="layui-inline">
                <table>
                    <tr>
                        <td>
                            <div onclick="choosefile('image_url')" style="width:341px ;height:180px;  background-image:url('/Content/images/upLoadbg.png');background-repeat:no-repeat; border:1px solid #c3c2c2;border-radius:5px; background-position:center center; background-size:50%;">
                                <img style="width:100%;" id="image_url_upload" src="" />
                                <input hidden="hidden" id="image_url" name="image_url" value="" />
                            </div>
                        </td>
                    </tr>

                </table>
            </div>
            <div class="layui-inline">
                说明：720px*380px像素的图片
            </div>
        </div>
    </div>


    <div class="form-group">
        <label for="inputPassword" class="col-sm-2 control-label">视频介绍：</label>
        <div class="col-sm-9">
            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.config.js"></script>
            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/ueditor.all.min.js"></script>

            <script type="text/javascript" charset="utf-8" src="/Content/plugins/utf8-net/lang/zh-cn/zh-cn.js"></script>
            <script id="editor" type="text/plain" style="width:690px;height:310px;">

            </script>

            <script type="text/javascript">
                function getContent() {
                    return UE.getEditor('editor').getContent();
                }

                //实例化编辑器
                //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
                var ue = UE.getEditor('editor');
                ue.ready(function () {
                    UE.getEditor('editor').setContent('', false);
                });
            </script>
        </div>
    </div>
    <textarea type="text" hidden="hidden" id="introduce" name="introduce"></textarea>


    <div class="form-group">
        <label class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-9">
            <input type="text" class="form-control" name="remark" placeholder="请输入备注">

        </div>
    </div>
}
<div class="site-demo-upbar" hidden="hidden">
    <input type="file" name="file" class="layui-upload-file" id="file">
</div>
<script>
    layui.use('upload', function () {
        layui.upload({
            ext: 'jpg',
            url: '/UploadFiles/Upload_MallProductTypeImg'
            , method: 'post' //上传接口的http类型
            , before: function (input) {
                show_loading();
            }
            , success: function (res) {
                close_loading();
                if (res.Isok) {
                    $("#" + image_id + "_upload").attr("src", "http://" + res.Obj);
                    $("#" + image_id + "").val($("#" + image_id + "_upload").attr("src"));
                }
                else {
                    alert(res.Msg);
                }
            }
        });
    });

    var image_id;
    function choosefile(_image_id) {
        image_id = _image_id;
        var f = document.getElementById("file");
        f.click();
    }
</script>

<script>
    function Save() {
        var introduction = UE.getEditor('editor').getContent();
        $("#introduce").val(introduction);
        if (!isNullOrUndefined($("#image_url").val())) {
            $("#form").submit()
        }
        else {
            show_msg("图片不能为空。")
        }
    }
    function OnBegin() {
        show_loading();
    }
    function OnSuccess(data) {
        close_loading();
        if (data.Isok) {
            window.location = "Oline_Index?time=" + new Date().toString();
        }
        else {
            show_msg("添加失败" + data.Msg)
        }
    }
</script>
