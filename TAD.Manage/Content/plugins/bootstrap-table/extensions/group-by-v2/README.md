# Table group-by-v2

Use Plugin: [bootstrap-table-group-by-v2](https://github.com/wenzhixin/bootstrap-table/tree/master/src/extensions/group-by-v2) </br>
You must include the bootstrap-table-group-by.css file in order to get the appropriate style

## Usage

```html
<script src="extensions/group-by-v2/bootstrap-table-group-by.js"></script>
```

## Options

### groupBy

* type: Boolean
* description: Set true to group the data by the field passed.
* default: `false`

### groupByField

* type: String
* description: Set the fields name that you want to group the data.
* default: ``