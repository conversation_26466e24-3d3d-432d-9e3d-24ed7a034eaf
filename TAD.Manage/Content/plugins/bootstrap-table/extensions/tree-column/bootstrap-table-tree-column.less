 .table:not(.table-condensed) > tbody > tr > td.treenode {
     padding-top: 0;
     padding-bottom: 0;
     border-bottom: solid #fff 1px;
 }
 
 .table:not(.table-condensed) > tbody > tr:last-child > td.treenode {
     border-bottom: none;
 }
 
 .treenode {
     .text {
         float: left;
         display: block;
         padding-top: 6px;
         padding-bottom: 6px;
     }
     .vertical,
     .vertical.last {
         float: left;
         display: block;
         width: 1px;
         border-left: dashed silver 1px;
         height: 38px;
         margin-left: 8px;
     }
     .vertical.last {
         height: 15px;
     }
     
     .space,
     .node {
         float: left;
         display: block;
         width: 15px;
         height: 5px;
         margin-top: 15px;
     }
     
     .node {
         border-top: dashed silver 1px;
     }
 }