{"name": "<PERSON><PERSON>", "version": "1.0.0", "description": "An extension which provides a sticky header for table columns when scrolling on a long page and / or table. Works for tables with many columns and narrow width with horizontal scrollbars too.", "url": "https://github.com/wenzhixin/bootstrap-table/tree/master/src/extensions/sticky-header", "example": "http://issues.wenzhixin.net.cn/bootstrap-table/#extensions/sticky-header.html", "plugins": [{"name": "bootstrap-table-sticky-header", "url": "https://github.com/wenzhixin/bootstrap-table/tree/master/src/extensions/sticky-header"}], "author": {"name": "vinzloh", "image": "https://avatars0.githubusercontent.com/u/5501845"}}