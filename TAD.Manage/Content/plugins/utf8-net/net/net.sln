
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2013
VisualStudioVersion = 12.0.21005.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "net", "http://localhost:7957", "{36F65A7F-64E7-4E05-BBC2-EAB6E4EDAF30}"
	ProjectSection(WebsiteProperties) = preProject
		UseIISExpress = "true"
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.0"
		Debug.AspNetCompiler.VirtualPath = "/localhost_7957"
		Debug.AspNetCompiler.PhysicalPath = "..\..\..\..\prj\ueditor\net\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_7957\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/localhost_7957"
		Release.AspNetCompiler.PhysicalPath = "..\..\..\..\prj\ueditor\net\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_7957\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		SlnRelativePath = "..\..\..\..\prj\ueditor\net\"
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{36F65A7F-64E7-4E05-BBC2-EAB6E4EDAF30}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{36F65A7F-64E7-4E05-BBC2-EAB6E4EDAF30}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
